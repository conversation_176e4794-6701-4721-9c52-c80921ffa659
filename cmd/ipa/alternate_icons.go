package ipa

import (
	"encoding/json"
	"fmt"
	"image"
	_ "image/jpeg"
	_ "image/png"
	"io"
	"os"
	"os/exec"
	"path/filepath"
	"runtime"
	"strings"
	"time"
)

const (
	ColorRed    = "\033[0;31m"
	ColorGreen  = "\033[0;32m"
	ColorYellow = "\033[1;33m"
	ColorNC     = "\033[0m"
)

type AlternateIconsConfig struct {
	IconPaths     []string
	ProjectPath   string
	OutputBaseDir string
	ProjectType   string // "unreal", "unity", or ""
}

type IconInfo struct {
	Name  string   `json:"name"`
	Files []string `json:"files"`
}

type ContentsJSON struct {
	Info struct {
		Author  string `json:"author"`
		Version int    `json:"version"`
	} `json:"info"`
}

type IconSetContentsJSON struct {
	Images []struct {
		Filename string `json:"filename"`
		Idiom    string `json:"idiom"`
		Platform string `json:"platform"`
		Size     string `json:"size"`
	} `json:"images"`
	Info struct {
		Author  string `json:"author"`
		Version int    `json:"version"`
	} `json:"info"`
}

func printError(msg string) {
	if runtime.GOOS == "windows" {
		fmt.Printf("[ERROR] %s\n", msg)
	} else {
		fmt.Printf("%s[ERROR]%s %s\n", ColorRed, ColorNC, msg)
	}
}

func printSuccess(msg string) {
	if runtime.GOOS == "windows" {
		fmt.Printf("[SUCCESS] %s\n", msg)
	} else {
		fmt.Printf("%s[SUCCESS]%s %s\n", ColorGreen, ColorNC, msg)
	}
}

func printInfo(msg string) {
	if runtime.GOOS == "windows" {
		fmt.Printf("[INFO] %s\n", msg)
	} else {
		fmt.Printf("%s[INFO]%s %s\n", ColorYellow, ColorNC, msg)
	}
}

func validateProjectPath(config *AlternateIconsConfig) error {
	if config.ProjectPath == "" {
		return nil
	}

	// Check if directory exists
	if _, err := os.Stat(config.ProjectPath); os.IsNotExist(err) {
		return fmt.Errorf("project path does not exist: %s", config.ProjectPath)
	}

	// Check if it's an Unreal project
	unrealPattern := filepath.Join(config.ProjectPath, "*.uproject")
	unrealMatches, err := filepath.Glob(unrealPattern)
	if err != nil {
		return fmt.Errorf("error checking for .uproject files: %v", err)
	}

	// Check if it's a Unity project
	assetsDir := filepath.Join(config.ProjectPath, "Assets")
	projectSettingsDir := filepath.Join(config.ProjectPath, "ProjectSettings")
	isUnityProject := false
	if _, err := os.Stat(assetsDir); err == nil {
		if _, err := os.Stat(projectSettingsDir); err == nil {
			isUnityProject = true
		}
	}

	if len(unrealMatches) > 0 {
		config.ProjectType = "unreal"
		unrealIOSResourcesDir := filepath.Join(config.ProjectPath, "Build", "IOS", "Resources")
		printInfo(fmt.Sprintf("检测到 Unreal 项目: %s", config.ProjectPath))
		printInfo(fmt.Sprintf("将复制资源到: %s", unrealIOSResourcesDir))
	} else if isUnityProject {
		config.ProjectType = "unity"
		unityEditorDir := filepath.Join(config.ProjectPath, "Assets", "Editor")
		printInfo(fmt.Sprintf("检测到 Unity 项目: %s", config.ProjectPath))
		printInfo(fmt.Sprintf("将复制资源到: %s/AlternateIcons/", unityEditorDir))
	} else {
		return fmt.Errorf("在路径中未找到有效项目: %s\n请确保指向有效的 Unity 或 Unreal Engine 项目目录", config.ProjectPath)
	}

	return nil
}

func setupOutputDirectories(config *AlternateIconsConfig) (string, string, string, error) {
	outputBaseDir := config.OutputBaseDir
	if outputBaseDir == "" {
		// Get current working directory
		scriptDir, err := os.Getwd()
		if err != nil {
			return "", "", "", fmt.Errorf("获取当前工作目录失败: %v", err)
		}
		outputBaseDir = filepath.Join(scriptDir, "out")
		printInfo("使用当前目录/out 作为输出基础目录")
	} else {
		printInfo(fmt.Sprintf("使用指定的输出目录: %s", outputBaseDir))
	}

	// Create output directory if it doesn't exist
	if _, err := os.Stat(outputBaseDir); os.IsNotExist(err) {
		if err := os.MkdirAll(outputBaseDir, 0755); err != nil {
			return "", "", "", fmt.Errorf("创建输出目录失败: %v", err)
		}
		printInfo(fmt.Sprintf("已创建输出目录: %s", outputBaseDir))
	}

	outputDir := filepath.Join(outputBaseDir, "Assets.xcassets")
	buildDir := filepath.Join(outputBaseDir, "build")
	uplFile := filepath.Join(outputBaseDir, "IOSAlternateIcons_UPL.xml")

	return outputDir, buildDir, uplFile, nil
}

func validateImage(imagePath string) error {
	imageName := filepath.Base(imagePath)
	printInfo(fmt.Sprintf("验证图片 %s...", imageName))

	// Open the image file
	file, err := os.Open(imagePath)
	if err != nil {
		return fmt.Errorf("无法打开图片文件 %s: %v", imageName, err)
	}
	defer file.Close()

	// Decode the image to get its dimensions
	img, _, err := image.DecodeConfig(file)
	if err != nil {
		return fmt.Errorf("无法解码图片 %s: %v", imageName, err)
	}

	// Check if dimensions are 1024x1024
	if img.Width != 1024 || img.Height != 1024 {
		return fmt.Errorf("%s 尺寸为 %dx%d，但需要 1024x1024", imageName, img.Width, img.Height)
	}

	printSuccess(fmt.Sprintf("%s 验证通过 (1024x1024)", imageName))
	return nil
}

func validateAllImages(iconPaths []string) error {
	for _, imagePath := range iconPaths {
		if _, err := os.Stat(imagePath); os.IsNotExist(err) {
			return fmt.Errorf("图片文件未找到: %s", imagePath)
		}
		if err := validateImage(imagePath); err != nil {
			return err
		}
	}
	return nil
}

func cleanupPreviousResults(outputDir, buildDir, uplFile string) {
	printInfo("清理之前的执行结果...")

	if _, err := os.Stat(outputDir); err == nil {
		os.RemoveAll(outputDir)
		printInfo("已删除之前的 Assets.xcassets 目录")
	}

	if _, err := os.Stat(buildDir); err == nil {
		os.RemoveAll(buildDir)
		printInfo("已删除之前的 build 目录")
	}

	if _, err := os.Stat(uplFile); err == nil {
		os.Remove(uplFile)
		printInfo("已删除之前的 IOSAlternateIcons_UPL.xml 文件")
	}
}

func copyFileForIcons(src, dst string) error {
	sourceFile, err := os.Open(src)
	if err != nil {
		return err
	}
	defer sourceFile.Close()

	destFile, err := os.Create(dst)
	if err != nil {
		return err
	}
	defer destFile.Close()

	_, err = io.Copy(destFile, sourceFile)
	return err
}

func createDirectoryStructure(outputDir, buildDir string, iconPaths []string) ([]string, error) {
	printInfo("创建目录结构...")

	// Create main directories
	if err := os.MkdirAll(outputDir, 0755); err != nil {
		return nil, err
	}
	if err := os.MkdirAll(buildDir, 0755); err != nil {
		return nil, err
	}

	// Create directories for each image
	var iconDirs []string
	for i := range iconPaths {
		var iconDir string
		if i == 0 {
			iconDir = filepath.Join(outputDir, "AppIcon.appiconset")
		} else {
			iconDir = filepath.Join(outputDir, fmt.Sprintf("AppIcon%d.appiconset", i+1))
		}
		iconDirs = append(iconDirs, iconDir)
		if err := os.MkdirAll(iconDir, 0755); err != nil {
			return nil, err
		}
	}

	return iconDirs, nil
}

func copyImages(iconPaths []string, iconDirs []string) error {
	printInfo("复制图片...")
	for i, imagePath := range iconPaths {
		imageName := filepath.Base(imagePath)
		iconDir := iconDirs[i]
		destPath := filepath.Join(iconDir, imageName)
		if err := copyFileForIcons(imagePath, destPath); err != nil {
			return fmt.Errorf("复制 %s 失败: %v", imageName, err)
		}
	}
	return nil
}

func createContentsJSON(outputDir string, iconPaths []string, iconDirs []string) error {
	printInfo("创建 Contents.json 文件...")

	// Create main Contents.json
	mainContents := ContentsJSON{}
	mainContents.Info.Author = "xcode"
	mainContents.Info.Version = 1

	mainData, err := json.MarshalIndent(mainContents, "", "  ")
	if err != nil {
		return err
	}

	mainPath := filepath.Join(outputDir, "Contents.json")
	if err := os.WriteFile(mainPath, mainData, 0644); err != nil {
		return err
	}

	// Create Contents.json for each icon set
	for i, imagePath := range iconPaths {
		imageName := filepath.Base(imagePath)
		iconDir := iconDirs[i]

		iconContents := IconSetContentsJSON{}
		iconContents.Images = []struct {
			Filename string `json:"filename"`
			Idiom    string `json:"idiom"`
			Platform string `json:"platform"`
			Size     string `json:"size"`
		}{
			{
				Filename: imageName,
				Idiom:    "universal",
				Platform: "ios",
				Size:     "1024x1024",
			},
		}
		iconContents.Info.Author = "xcode"
		iconContents.Info.Version = 1

		iconData, err := json.MarshalIndent(iconContents, "", "  ")
		if err != nil {
			return err
		}

		iconPath := filepath.Join(iconDir, "Contents.json")
		if err := os.WriteFile(iconPath, iconData, 0644); err != nil {
			return err
		}
	}

	return nil
}

func compileAssets(outputDir, buildDir string) error {
	// Check if we're on macOS and xcrun is available
	if runtime.GOOS != "darwin" {
		printInfo("跳过 xcrun actool 编译 (非 macOS 系统)")
		printInfo("Assets.xcassets 结构已创建，但编译需要 macOS 和 Xcode")
		return nil
	}

	printInfo("运行 xcrun actool 编译资源...")

	partialPlistPath := filepath.Join(buildDir, "partial.plist")

	cmd := exec.Command("xcrun", "actool",
		outputDir,
		"--compile", buildDir,
		"--app-icon", "AppIcon",
		"--include-all-app-icons",
		"--compress-pngs",
		"--enable-on-demand-resources", "YES",
		"--development-region", "English",
		"--target-device", "iphone",
		"--target-device", "ipad",
		"--minimum-deployment-target", "13.0",
		"--platform", "iphoneos",
		"--output-partial-info-plist", partialPlistPath,
		"--errors", "--warnings", "--notices",
		"--output-format", "xml1")

	if err := cmd.Run(); err != nil {
		return fmt.Errorf("使用 xcrun actool 编译图标失败: %v", err)
	}

	printSuccess("图标编译成功！")
	printInfo("输出文件:")
	printInfo("  - Assets.xcassets/ (源文件)")
	printInfo("  - build/ (编译输出)")
	printInfo("  - build/partial.plist (部分信息 plist)")

	return nil
}

func generateUPLFile(buildDir, uplFile string) error {
	printInfo("为 Unreal Engine 生成 UPL 文件...")

	// Check if we're on macOS for plist processing
	if runtime.GOOS != "darwin" {
		printInfo("生成基础 UPL 文件 (完整 plist 处理需要 macOS)")
		return generateBasicUPLFile(buildDir, uplFile)
	}

	// Find PNG files
	pattern := filepath.Join(buildDir, "*.png")
	pngFiles, err := filepath.Glob(pattern)
	if err != nil {
		return fmt.Errorf("查找 PNG 文件时出错: %v", err)
	}

	assetsCar := filepath.Join(buildDir, "Assets.car")
	partialPlist := filepath.Join(buildDir, "partial.plist")

	// Check required files exist
	if _, err := os.Stat(assetsCar); os.IsNotExist(err) {
		return fmt.Errorf("在构建目录中未找到 Assets.car")
	}
	if _, err := os.Stat(partialPlist); os.IsNotExist(err) {
		return fmt.Errorf("在构建目录中未找到 partial.plist")
	}

	printInfo("从 partial.plist 提取图标信息...")

	// Convert plist to JSON
	cmd := exec.Command("plutil", "-convert", "json", "-o", "-", partialPlist)
	plistJSON, err := cmd.Output()
	if err != nil {
		return fmt.Errorf("转换 plist 到 JSON 失败: %v", err)
	}

	// Parse JSON to extract icon information
	var plistData map[string]interface{}
	if err := json.Unmarshal(plistJSON, &plistData); err != nil {
		return fmt.Errorf("解析 plist JSON 失败: %v", err)
	}

	// Extract icon information
	var altIcons []IconInfo
	var altIconsIPad []IconInfo

	// Extract alternate icons for iPhone
	if cfBundleIcons, ok := plistData["CFBundleIcons"].(map[string]interface{}); ok {
		if altIconsData, ok := cfBundleIcons["CFBundleAlternateIcons"].(map[string]interface{}); ok {
			for name, iconData := range altIconsData {
				if iconMap, ok := iconData.(map[string]interface{}); ok {
					if iconFiles, ok := iconMap["CFBundleIconFiles"].([]interface{}); ok {
						var files []string
						for _, file := range iconFiles {
							if fileStr, ok := file.(string); ok {
								files = append(files, fileStr)
							}
						}
						altIcons = append(altIcons, IconInfo{Name: name, Files: files})
					}
				}
			}
		}
	}

	// Extract alternate icons for iPad
	if cfBundleIconsIPad, ok := plistData["CFBundleIcons~ipad"].(map[string]interface{}); ok {
		if altIconsData, ok := cfBundleIconsIPad["CFBundleAlternateIcons"].(map[string]interface{}); ok {
			for name, iconData := range altIconsData {
				if iconMap, ok := iconData.(map[string]interface{}); ok {
					if iconFiles, ok := iconMap["CFBundleIconFiles"].([]interface{}); ok {
						var files []string
						for _, file := range iconFiles {
							if fileStr, ok := file.(string); ok {
								files = append(files, fileStr)
							}
						}
						altIconsIPad = append(altIconsIPad, IconInfo{Name: name, Files: files})
					}
				}
			}
		}
	}

	return generateFullUPLFile(uplFile, pngFiles, altIcons, altIconsIPad, plistData)
}

func generateBasicUPLFile(buildDir, uplFile string) error {
	// Find PNG files
	pattern := filepath.Join(buildDir, "*.png")
	pngFiles, err := filepath.Glob(pattern)
	if err != nil {
		return fmt.Errorf("查找 PNG 文件时出错: %v", err)
	}

	// Generate basic UPL file
	uplContent := strings.Builder{}
	uplContent.WriteString(`<?xml version="1.0" encoding="utf-8"?>
<root>
    <init>
        <log text="copy Assets.car and icon files..."/>
        <copyFile src="$S(ProjectDir)/Build/IOS/Resources/Assets.car" dst="$S(BuildDir)/Assets.car" />
`)

	// Add copyFile entries for PNG files
	for _, pngFile := range pngFiles {
		filename := filepath.Base(pngFile)
		uplContent.WriteString(fmt.Sprintf(`        <copyFile src="$S(ProjectDir)/Build/IOS/Resources/%s" dst="$S(BuildDir)/%s" />
`, filename, filename))
	}

	uplContent.WriteString(`    </init>

    <iosPListUpdates>
        <addElements tag="dict" once="true">
            <!-- Basic alternate icons structure -->
            <!-- Note: Full plist processing requires macOS with plutil -->
            <key>CFBundleIcons</key>
            <dict>
                <key>CFBundleAlternateIcons</key>
                <dict>
                    <!-- Add your alternate icons here -->
                </dict>
                <key>CFBundlePrimaryIcon</key>
                <dict>
                    <key>CFBundleIconFiles</key>
                    <array>
                        <string>AppIcon</string>
                    </array>
                    <key>CFBundleIconName</key>
                    <string>AppIcon</string>
                </dict>
            </dict>
        </addElements>
    </iosPListUpdates>
</root>
`)

	if err := os.WriteFile(uplFile, []byte(uplContent.String()), 0644); err != nil {
		return fmt.Errorf("写入 UPL 文件失败: %v", err)
	}

	printSuccess(fmt.Sprintf("已生成基础 UPL 文件: %s", uplFile))
	printInfo("注意: 完整的 plist 处理需要在安装了 Xcode 的 macOS 上运行此工具")
	return nil
}

func generateFullUPLFile(uplFile string, pngFiles []string, altIcons, altIconsIPad []IconInfo, plistData map[string]interface{}) error {
	// Generate UPL file
	uplContent := strings.Builder{}
	uplContent.WriteString(`<?xml version="1.0" encoding="utf-8"?>
<root>
    <init>
        <log text="copy Assets.car and icon files..."/>
        <copyFile src="$S(ProjectDir)/Build/IOS/Resources/Assets.car" dst="$S(BuildDir)/Assets.car" />
`)

	// Add copyFile entries for PNG files
	for _, pngFile := range pngFiles {
		filename := filepath.Base(pngFile)
		uplContent.WriteString(fmt.Sprintf(`        <copyFile src="$S(ProjectDir)/Build/IOS/Resources/%s" dst="$S(BuildDir)/%s" />
`, filename, filename))
	}

	uplContent.WriteString(`    </init>

    <iosPListUpdates>
        <addElements tag="dict" once="true">
            <key>CFBundleIcons</key>
                <dict>
                    <key>CFBundleAlternateIcons</key>
                    <dict>
`)

	// Add alternate icons for iPhone
	for _, icon := range altIcons {
		uplContent.WriteString(fmt.Sprintf(`                        <key>%s</key>
                        <dict>
                            <key>CFBundleIconFiles</key>
                            <array>
`, icon.Name))
		for _, file := range icon.Files {
			uplContent.WriteString(fmt.Sprintf(`                                <string>%s</string>
`, file))
		}
		uplContent.WriteString(fmt.Sprintf(`                            </array>
                            <key>CFBundleIconName</key>
                            <string>%s</string>
                        </dict>
`, icon.Name))
	}

	uplContent.WriteString(`                    </dict>
                    <key>CFBundlePrimaryIcon</key>
                    <dict>
                        <key>CFBundleIconFiles</key>
                        <array>
`)

	// Extract and add primary icon files for iPhone
	if cfBundleIcons, ok := plistData["CFBundleIcons"].(map[string]interface{}); ok {
		if primaryIcon, ok := cfBundleIcons["CFBundlePrimaryIcon"].(map[string]interface{}); ok {
			if iconFiles, ok := primaryIcon["CFBundleIconFiles"].([]interface{}); ok {
				for _, file := range iconFiles {
					if fileStr, ok := file.(string); ok {
						uplContent.WriteString(fmt.Sprintf(`                            <string>%s</string>
`, fileStr))
					}
				}
			}
		}
	}

	uplContent.WriteString(`                        </array>
                        <key>CFBundleIconName</key>
                        <string>AppIcon</string>
                    </dict>
                </dict>
                <key>CFBundleIcons~ipad</key>
                <dict>
                    <key>CFBundleAlternateIcons</key>
                    <dict>
`)

	// Add alternate icons for iPad
	for _, icon := range altIconsIPad {
		uplContent.WriteString(fmt.Sprintf(`                        <key>%s</key>
                        <dict>
                            <key>CFBundleIconFiles</key>
                            <array>
`, icon.Name))
		for _, file := range icon.Files {
			uplContent.WriteString(fmt.Sprintf(`                                <string>%s</string>
`, file))
		}
		uplContent.WriteString(fmt.Sprintf(`                            </array>
                            <key>CFBundleIconName</key>
                            <string>%s</string>
                        </dict>
`, icon.Name))
	}

	uplContent.WriteString(`                    </dict>
                    <key>CFBundlePrimaryIcon</key>
                    <dict>
                        <key>CFBundleIconFiles</key>
                        <array>
`)

	// Extract and add primary icon files for iPad
	if cfBundleIconsIPad, ok := plistData["CFBundleIcons~ipad"].(map[string]interface{}); ok {
		if primaryIcon, ok := cfBundleIconsIPad["CFBundlePrimaryIcon"].(map[string]interface{}); ok {
			if iconFiles, ok := primaryIcon["CFBundleIconFiles"].([]interface{}); ok {
				for _, file := range iconFiles {
					if fileStr, ok := file.(string); ok {
						uplContent.WriteString(fmt.Sprintf(`                            <string>%s</string>
`, fileStr))
					}
				}
			}
		}
	}

	uplContent.WriteString(`                        </array>
                        <key>CFBundleIconName</key>
                        <string>AppIcon</string>
                    </dict>
                </dict>
        </addElements>
    </iosPListUpdates>
</root>
`)

	if err := os.WriteFile(uplFile, []byte(uplContent.String()), 0644); err != nil {
		return fmt.Errorf("failed to write UPL file: %v", err)
	}

	printSuccess(fmt.Sprintf("UPL file generated: %s", uplFile))
	return nil
}

func copyToUnrealProject(projectPath, buildDir string) error {
	if projectPath == "" {
		return nil
	}

	printInfo("复制文件到 Unreal 项目...")

	unrealIOSResourcesDir := filepath.Join(projectPath, "Build", "IOS", "Resources")

	// Create resources directory if it doesn't exist
	if err := os.MkdirAll(unrealIOSResourcesDir, 0755); err != nil {
		return fmt.Errorf("创建资源目录失败: %v", err)
	}

	// Copy Assets.car if it exists
	assetsCar := filepath.Join(buildDir, "Assets.car")
	if _, err := os.Stat(assetsCar); err == nil {
		destPath := filepath.Join(unrealIOSResourcesDir, "Assets.car")
		if err := copyFileForIcons(assetsCar, destPath); err != nil {
			return fmt.Errorf("复制 Assets.car 失败: %v", err)
		}
		printSuccess(fmt.Sprintf("已复制 Assets.car 到 %s/", unrealIOSResourcesDir))
	} else {
		printInfo("在构建目录中未找到 Assets.car (非 macOS 平台正常现象)")
	}

	// Copy PNG files
	pattern := filepath.Join(buildDir, "*.png")
	pngFiles, err := filepath.Glob(pattern)
	if err != nil {
		return fmt.Errorf("查找 PNG 文件时出错: %v", err)
	}

	pngCount := 0
	for _, pngFile := range pngFiles {
		filename := filepath.Base(pngFile)
		destPath := filepath.Join(unrealIOSResourcesDir, filename)
		if err := copyFileForIcons(pngFile, destPath); err != nil {
			return fmt.Errorf("复制 %s 失败: %v", filename, err)
		}
		printSuccess(fmt.Sprintf("已复制 %s 到 %s/", filename, unrealIOSResourcesDir))
		pngCount++
	}

	if pngCount == 0 {
		printInfo("在构建目录中未找到 PNG 文件 (非 macOS 平台正常现象)")
	} else {
		printSuccess(fmt.Sprintf("已复制 %d 个 PNG 文件到 Unreal 项目", pngCount))
	}

	printInfo("文件已成功复制到 Unreal 项目！")
	printInfo("后续步骤:")
	printInfo("  1. 将 IOSAlternateIcons_UPL.xml 放置到您的 Unreal 模块目录中 (例如: Source/YourModule/)")
	printInfo("  2. 在您的 .Build.cs 文件中添加以下代码:")
	printInfo("     if (Target.Platform == UnrealTargetPlatform.IOS)")
	printInfo("     {")
	printInfo("         AdditionalPropertiesForReceipt.Add(\"IOSPlugin\", Path.Combine(ModuleDirectory, \"IOSAlternateIcons_UPL.xml\"));")
	printInfo("     }")

	return nil
}

func copyDirRecursively(src, dst string) error {
	entries, err := os.ReadDir(src)
	if err != nil {
		return err
	}

	if err := os.MkdirAll(dst, 0755); err != nil {
		return err
	}

	for _, entry := range entries {
		srcPath := filepath.Join(src, entry.Name())
		dstPath := filepath.Join(dst, entry.Name())

		if entry.IsDir() {
			if err := copyDirRecursively(srcPath, dstPath); err != nil {
				return err
			}
		} else {
			if err := copyFileForIcons(srcPath, dstPath); err != nil {
				return err
			}
		}
	}
	return nil
}

func generateUnityPostProcessScript(alternateIconsDir string) error {
	printInfo("生成 Unity PostProcessBuild 脚本...")

	scriptContent := `using System.IO;
using UnityEngine;
using UnityEditor;
using UnityEditor.Build;
using UnityEditor.Build.Reporting;
using UnityEditor.iOS.Xcode;

namespace AlternateIcons
{
    public class IOSAlternateIconsPostProcess : IPostprocessBuildWithReport
    {
        public int callbackOrder => 999;

        public void OnPostprocessBuild(BuildReport report)
        {
            if (report.summary.platform != BuildTarget.iOS) return;

            string pathToBuiltProject = report.summary.outputPath;
            ReplaceAppIcons(pathToBuiltProject);
            SetIncludeAllAppIconAssets(pathToBuiltProject);
        }

        private static void ReplaceAppIcons(string pathToBuiltProject)
        {
            string sourceAssetsPath = Path.Combine(Application.dataPath, "Editor/AlternateIcons/Assets.xcassets");
            string destAssetsPath = Path.Combine(pathToBuiltProject, "Unity-iPhone/Images.xcassets");

            if (Directory.Exists(sourceAssetsPath))
            {
                if (Directory.Exists(destAssetsPath))
                {
                    Directory.Delete(destAssetsPath, true);
                }
                DirectoryCopy(sourceAssetsPath, destAssetsPath);
                Debug.Log($"[AlternateIcons] 已从 {sourceAssetsPath} 替换 iOS 应用图标到 {destAssetsPath}");
            }
            else
            {
                Debug.LogWarning($"[AlternateIcons] 未找到源资源路径: {sourceAssetsPath}");
            }
        }

        private static void DirectoryCopy(string sourceDirName, string destDirName)
        {
            Directory.CreateDirectory(destDirName);
            foreach (string file in Directory.GetFiles(sourceDirName))
            {
                File.Copy(file, Path.Combine(destDirName, Path.GetFileName(file)), true);
            }
            foreach (string dir in Directory.GetDirectories(sourceDirName))
            {
                DirectoryCopy(dir, Path.Combine(destDirName, Path.GetFileName(dir)));
            }
        }

        private static void SetIncludeAllAppIconAssets(string pathToBuiltProject)
        {
            string projPath = PBXProject.GetPBXProjectPath(pathToBuiltProject);
            PBXProject proj = new PBXProject();
            proj.ReadFromFile(projPath);
            string targetGuid = proj.GetUnityMainTargetGuid();

            proj.AddBuildProperty(targetGuid, "ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS", "YES");

            proj.WriteToFile(projPath);
            Debug.Log("[AlternateIcons] 已设置 ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = YES");
        }
    }
}
`

	scriptPath := filepath.Join(alternateIconsDir, "IOSAlternateIconsPostProcess.cs")
	if err := os.WriteFile(scriptPath, []byte(scriptContent), 0644); err != nil {
		return fmt.Errorf("failed to write Unity PostProcess script: %v", err)
	}

	printSuccess(fmt.Sprintf("已生成 Unity PostProcess 脚本: %s", scriptPath))
	return nil
}

func copyToUnityProject(projectPath, outputDir string) error {
	if projectPath == "" {
		return nil
	}

	printInfo("复制文件到 Unity 项目...")

	// Create AlternateIcons directory in Assets/Editor
	unityEditorDir := filepath.Join(projectPath, "Assets", "Editor")
	alternateIconsDir := filepath.Join(unityEditorDir, "AlternateIcons")

	if err := os.MkdirAll(alternateIconsDir, 0755); err != nil {
		return fmt.Errorf("failed to create AlternateIcons directory: %v", err)
	}

	// Copy Assets.xcassets to Unity project
	sourceAssetsDir := outputDir
	destAssetsDir := filepath.Join(alternateIconsDir, "Assets.xcassets")

	if err := copyDirRecursively(sourceAssetsDir, destAssetsDir); err != nil {
		return fmt.Errorf("failed to copy Assets.xcassets to Unity project: %v", err)
	}

	printSuccess(fmt.Sprintf("已复制 Assets.xcassets 到 %s", destAssetsDir))

	// Generate Unity PostProcess script
	if err := generateUnityPostProcessScript(alternateIconsDir); err != nil {
		return err
	}

	printInfo("文件已成功复制到 Unity 项目！")
	printInfo("后续步骤:")
	printInfo("  1. PostProcessBuild 脚本已生成在 Assets/Editor/AlternateIcons/ 目录中")
	printInfo("  2. 确保已安装 Unity iOS Build Support 包")
	printInfo("  3. 脚本将在构建过程中自动替换 iOS 应用图标")
	printInfo("  4. 构建 Unity 项目到 iOS 平台以应用备用图标")

	return nil
}

// GenerateAlternateIconsExe is the main function for generating alternate icons
func GenerateAlternateIconsExe(iconPaths []string, projectPath, outputBaseDir string) error {
	config := &AlternateIconsConfig{
		IconPaths:     iconPaths,
		ProjectPath:   projectPath,
		OutputBaseDir: outputBaseDir,
	}

	printInfo("开始图标生成过程...")

	// Validate project path
	if err := validateProjectPath(config); err != nil {
		return err
	}

	// Setup output directories
	outputDir, buildDir, uplFile, err := setupOutputDirectories(config)
	if err != nil {
		return err
	}

	// Validate all images
	if err := validateAllImages(config.IconPaths); err != nil {
		return err
	}

	// Cleanup previous results
	cleanupPreviousResults(outputDir, buildDir, uplFile)

	// Wait a moment
	time.Sleep(1 * time.Second)

	// Create directory structure
	iconDirs, err := createDirectoryStructure(outputDir, buildDir, config.IconPaths)
	if err != nil {
		return fmt.Errorf("failed to create directory structure: %v", err)
	}

	// Copy images
	if err := copyImages(config.IconPaths, iconDirs); err != nil {
		return err
	}

	// Create Contents.json files
	if err := createContentsJSON(outputDir, config.IconPaths, iconDirs); err != nil {
		return fmt.Errorf("failed to create Contents.json files: %v", err)
	}

	printSuccess("Assets.xcassets 结构创建成功！")

	// Compile assets (only on macOS)
	if err := compileAssets(outputDir, buildDir); err != nil {
		return err
	}

	// Generate UPL file
	if err := generateUPLFile(buildDir, uplFile); err != nil {
		return err
	}

	// Copy to project if specified
	if config.ProjectPath != "" {
		switch config.ProjectType {
		case "unreal":
			if err := copyToUnrealProject(config.ProjectPath, buildDir); err != nil {
				return err
			}
		case "unity":
			if err := copyToUnityProject(config.ProjectPath, outputDir); err != nil {
				return err
			}
		}
	}

	printSuccess("图标生成过程完成！")
	printInfo("生成的文件:")
	printInfo(fmt.Sprintf("  - %s (源资源目录)", outputDir))
	printInfo(fmt.Sprintf("  - %s (编译后的图标和资源)", buildDir))
	printInfo(fmt.Sprintf("  - %s (Unreal Engine UPL 文件)", uplFile))
	if config.ProjectPath != "" {
		switch config.ProjectType {
		case "unreal":
			unrealIOSResourcesDir := filepath.Join(config.ProjectPath, "Build", "IOS", "Resources")
			printInfo(fmt.Sprintf("  - 文件已复制到: %s", unrealIOSResourcesDir))
		case "unity":
			unityAlternateIconsDir := filepath.Join(config.ProjectPath, "Assets", "Editor", "AlternateIcons")
			printInfo(fmt.Sprintf("  - 文件已复制到: %s", unityAlternateIconsDir))
		}
	}

	return nil
}
