package ipa

import (
	"onetools/cmd/utility"
	"os"
	"path/filepath"
	"strings"
)

// deleteFilesFromApp 从.app目录中删除指定的文件或文件夹
func deleteFilesFromApp(appPath string, deleteFilePaths string) error {
	if deleteFilePaths == "" {
		return nil
	}

	logInfo("开始删除指定的文件或文件夹...")

	// 分割路径字符串
	paths := strings.Split(deleteFilePaths, ",")

	for _, path := range paths {
		// 去除空格
		path = strings.TrimSpace(path)
		if path == "" {
			continue
		}

		// 构建完整路径
		fullPath := filepath.Join(appPath, path)

		// 检查文件或目录是否存在
		if !utility.IsExist(fullPath) {
			logWarning("文件或目录不存在，跳过删除: %s", path)
			continue
		}

		// 判断是文件还是目录
		isDir := utility.IsDir(fullPath)

		// 执行删除操作
		var err error
		if isDir {
			logInfo("删除目录: %s", path)
			err = os.RemoveAll(fullPath)
		} else {
			logInfo("删除文件: %s", path)
			err = os.Remove(fullPath)
		}

		if err != nil {
			logWarning("删除失败: %s, 错误: %v", path, err)
		} else {
			logSuccess("成功删除: %s", path)
		}
	}

	return nil
}
