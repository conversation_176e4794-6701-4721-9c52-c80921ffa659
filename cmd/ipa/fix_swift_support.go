package ipa

import (
	"archive/zip"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strings"
	"time"
)

// 日志格式化
func logInfo(format string, args ...interface{}) {
	timeStr := time.Now().Format("2006-01-02 15:04:05")
	fmt.Printf("[%s] %s\n", timeStr, fmt.Sprintf(format, args...))
}

func logError(format string, args ...interface{}) {
	timeStr := time.Now().Format("2006-01-02 15:04:05")
	fmt.Printf("[%s] [ERROR] %s\n", timeStr, fmt.Sprintf(format, args...))
}

func logWarning(format string, args ...interface{}) {
	timeStr := time.Now().Format("2006-01-02 15:04:05")
	fmt.Printf("[%s] [WARNING] %s\n", timeStr, fmt.Sprintf(format, args...))
}

func logSuccess(format string, args ...interface{}) {
	timeStr := time.Now().Format("2006-01-02 15:04:05")
	fmt.Printf("[%s] [SUCCESS] %s\n", timeStr, fmt.Sprintf(format, args...))
}

// 目录结构
type Config struct {
	IPAPath      string
	TempDir      string
	AppName      string
	ForceUpdate  bool
	ToolchainDir string
	NeedUpdate   bool // 是否需要更新SwiftSupport
}

// 清理临时目录
func cleanup(config *Config) {
	if config.TempDir != "" {
		logInfo("清理临时目录: %s", config.TempDir)
		os.RemoveAll(config.TempDir)
	}
}

// 获取应用名称
func getAppName(tempDir string) (string, error) {
	var appName string
	err := filepath.Walk(filepath.Join(tempDir, "Payload"), func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		if info.IsDir() && strings.HasSuffix(info.Name(), ".app") {
			appName = strings.TrimSuffix(info.Name(), ".app")
			return io.EOF // 使用EOF作为找到结果的信号
		}
		return nil
	})
	if err == io.EOF {
		err = nil
	}
	if appName == "" {
		return "", fmt.Errorf("未找到.app文件")
	}
	return appName, err
}

// 复制文件
func copyFile(src, dst string) error {
	in, err := os.Open(src)
	if err != nil {
		return err
	}
	defer in.Close()

	out, err := os.Create(dst)
	if err != nil {
		return err
	}
	defer out.Close()

	_, err = io.Copy(out, in)
	if err != nil {
		return err
	}
	return out.Close()
}

// 检查SwiftSupport状态
func checkSwiftSupport(config *Config) error {
	frameworksPath := filepath.Join(config.TempDir, "Payload", config.AppName+".app", "Frameworks")
	swiftSupportPath := filepath.Join(config.TempDir, "SwiftSupport", "iphoneos")

	// 获取Frameworks目录中的Swift库数量
	var frameworkSwiftLibs []string
	err := filepath.Walk(frameworksPath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		if !info.IsDir() && strings.HasPrefix(info.Name(), "libswift") && strings.HasSuffix(info.Name(), ".dylib") {
			frameworkSwiftLibs = append(frameworkSwiftLibs, info.Name())
		}
		return nil
	})
	if err != nil {
		return err
	}

	// 如果没有Swift库，不需要SwiftSupport
	if len(frameworkSwiftLibs) == 0 {
		logInfo("应用不包含Swift库，无需处理SwiftSupport")
		return nil
	}

	// 检查SwiftSupport目录是否存在
	swiftSupportExists := false
	if _, err := os.Stat(swiftSupportPath); err == nil {
		swiftSupportExists = true
	}

	// 如果强制更新，直接标记需要更新
	if config.ForceUpdate {
		logInfo("强制更新模式，将重新生成SwiftSupport")
		config.NeedUpdate = true
		return nil
	}

	// 如果SwiftSupport不存在，需要添加
	if !swiftSupportExists {
		logInfo("未找到SwiftSupport，需要添加")
		config.NeedUpdate = true
		return nil
	}

	// 检查SwiftSupport中的文件数量是否匹配
	var swiftSupportLibs []string
	err = filepath.Walk(swiftSupportPath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		if !info.IsDir() && strings.HasSuffix(info.Name(), ".dylib") {
			swiftSupportLibs = append(swiftSupportLibs, info.Name())
		}
		return nil
	})
	if err != nil {
		return err
	}

	// 比较文件数量和名称
	if len(frameworkSwiftLibs) != len(swiftSupportLibs) {
		logInfo("SwiftSupport库文件数量不匹配 (Frameworks: %d, SwiftSupport: %d)，需要更新",
			len(frameworkSwiftLibs), len(swiftSupportLibs))
		config.NeedUpdate = true
		return nil
	}

	// 检查每个库文件是否都存在
	for _, lib := range frameworkSwiftLibs {
		found := false
		for _, supportLib := range swiftSupportLibs {
			if lib == supportLib {
				found = true
				break
			}
		}
		if !found {
			logInfo("在SwiftSupport中未找到库文件: %s，需要更新", lib)
			config.NeedUpdate = true
			return nil
		}
	}

	logSuccess("现有SwiftSupport完整，无需更新")
	return nil
}

// 检查并处理SwiftSupport
func processSwiftSupport(config *Config) error {
	frameworksPath := filepath.Join(config.TempDir, "Payload", config.AppName+".app", "Frameworks")
	swiftSupportPath := filepath.Join(config.TempDir, "SwiftSupport", "iphoneos")

	// 确保SwiftSupport目录存在
	if err := os.MkdirAll(swiftSupportPath, 0755); err != nil {
		return err
	}

	// 获取Frameworks目录中的Swift库文件
	var swiftLibs []string
	err := filepath.Walk(frameworksPath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		if !info.IsDir() && strings.HasPrefix(info.Name(), "libswift") && strings.HasSuffix(info.Name(), ".dylib") {
			swiftLibs = append(swiftLibs, info.Name())
		}
		return nil
	})
	if err != nil {
		return err
	}

	logInfo("在Frameworks目录中找到 %d 个Swift库文件", len(swiftLibs))

	// 处理每个Swift库文件
	for _, lib := range swiftLibs {
		found := false
		swiftDirs, err := filepath.Glob(filepath.Join(config.ToolchainDir, "swift-*"))
		if err != nil {
			return err
		}

		for _, swiftDir := range swiftDirs {
			srcPath := filepath.Join(swiftDir, "iphoneos", lib)
			if _, err := os.Stat(srcPath); err == nil {
				dstPath := filepath.Join(swiftSupportPath, lib)
				if err := copyFile(srcPath, dstPath); err != nil {
					return err
				}
				found = true
				logSuccess("成功复制: %s", lib)
				break
			}
		}

		if !found {
			logWarning("在swift工具链目录中未找到 %s", lib)
		}
	}

	return nil
}

// 创建新的IPA文件
func createNewIPA(config *Config) error {
	timeStr := time.Now().Format("20060102_150405")
	newIPAPath := filepath.Join(filepath.Dir(config.IPAPath), fmt.Sprintf("%s_%s.ipa", config.AppName, timeStr))

	// 创建新的zip文件
	zipfile, err := os.Create(newIPAPath)
	if err != nil {
		return err
	}
	defer zipfile.Close()

	zipWriter := zip.NewWriter(zipfile)
	defer zipWriter.Close()

	// 计算总文件数（排除 temp.ipa）
	var totalFiles int
	err = filepath.Walk(config.TempDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		if !info.IsDir() && info.Name() != "temp.ipa" {
			totalFiles++
		}
		return nil
	})
	if err != nil {
		return err
	}

	// 压缩文件
	currentFile := 0
	err = filepath.Walk(config.TempDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// 跳过 temp.ipa 文件
		if info.Name() == "temp.ipa" {
			return nil
		}

		// 获取相对路径
		relPath, err := filepath.Rel(config.TempDir, path)
		if err != nil {
			return err
		}

		if info.IsDir() {
			return nil
		}

		currentFile++
		percentage := float64(currentFile) * 100 / float64(totalFiles)
		fmt.Printf("\r正在压缩文件: %d/%d (%.0f%%)", currentFile, totalFiles, percentage)

		// 创建新的zip文件
		header, err := zip.FileInfoHeader(info)
		if err != nil {
			return err
		}
		header.Name = relPath
		header.Method = zip.Deflate

		writer, err := zipWriter.CreateHeader(header)
		if err != nil {
			return err
		}

		file, err := os.Open(path)
		if err != nil {
			return err
		}
		defer file.Close()

		_, err = io.Copy(writer, file)
		return err
	})

	fmt.Println() // 换行
	if err != nil {
		return err
	}

	logSuccess("压缩完成")
	logInfo("新的IPA文件已保存为: %s", newIPAPath)
	return nil
}

func FixSwiftSupportExe(ipaPath string, forceUpdate bool) {
	// 初始化配置
	config := &Config{
		IPAPath:      ipaPath,
		ForceUpdate:  forceUpdate,
		ToolchainDir: "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib",
	}

	// 确保是绝对路径
	if !filepath.IsAbs(config.IPAPath) {
		absPath, err := filepath.Abs(config.IPAPath)
		if err != nil {
			logError("无法获取IPA文件的绝对路径: %v", err)
			os.Exit(1)
		}
		config.IPAPath = absPath
	}

	// 设置临时目录
	config.TempDir = filepath.Join(filepath.Dir(config.IPAPath), "fixSwiftSupportTemp")
	defer cleanup(config)

	// 清理并创建临时目录
	logInfo("清理并创建临时目录: %s", config.TempDir)
	if err := os.RemoveAll(config.TempDir); err != nil {
		logError("无法清理临时目录: %v", err)
		os.Exit(1)
	}
	if err := os.MkdirAll(config.TempDir, 0755); err != nil {
		logError("无法创建临时目录: %v", err)
		os.Exit(1)
	}

	// 复制并解压IPA文件
	logInfo("复制并解压IPA文件...")
	if err := copyFile(config.IPAPath, filepath.Join(config.TempDir, "temp.ipa")); err != nil {
		logError("无法复制IPA文件: %v", err)
		os.Exit(1)
	}

	if err := UnzipIPAExe(filepath.Join(config.TempDir, "temp.ipa"), config.TempDir); err != nil {
		logError("解压IPA文件失败: %v", err)
		os.Exit(1)
	}

	// 获取应用名称
	appName, err := getAppName(config.TempDir)
	if err != nil {
		logError("获取应用名称失败: %v", err)
		os.Exit(1)
	}
	config.AppName = appName
	logInfo("从Payload中提取的应用名称: %s", config.AppName)

	// 检查SwiftSupport状态
	if err := checkSwiftSupport(config); err != nil {
		logError("检查SwiftSupport失败: %v", err)
		os.Exit(1)
	}

	// 如果不需要更新，直接使用原文件
	if !config.NeedUpdate {
		logSuccess("SwiftSupport验证通过，保留原IPA文件")
		logInfo("=== 处理总结 ===")
		logInfo("原始IPA: %s (无需更新)", config.IPAPath)
		logInfo("================")
		cleanup(config)
		os.Exit(0)
	}

	// 需要更新时，处理SwiftSupport
	if err := processSwiftSupport(config); err != nil {
		logError("处理SwiftSupport失败: %v", err)
		os.Exit(1)
	}

	// 创建新的IPA文件
	if err := createNewIPA(config); err != nil {
		logError("创建新的IPA文件失败: %v", err)
		os.Exit(1)
	}

	// 输出处理总结
	logInfo("=== 处理总结 ===")
	logInfo("原始IPA: %s", config.IPAPath)
	logInfo("临时目录: %s (已清理)", config.TempDir)
	logInfo("================")
}
