package ipa

import (
	"archive/zip"
	"fmt"
	"io"
	"os"
	"path/filepath"
)

// 解压IPA文件
func UnzipIPAExe(zipPath, destPath string) error {
	reader, err := zip.OpenReader(zipPath)
	if err != nil {
		return fmt.Errorf("无法打开 IPA 文件: %w", err)
	}
	defer reader.Close()

	if err := os.MkdirAll(destPath, 0755); err != nil {
		return fmt.Errorf("无法创建目标目录: %w", err)
	}

	totalFiles := 0
	// 统计文件数量，排除文件夹
	for _, file := range reader.File {
		if !file.FileInfo().IsDir() {
			totalFiles++
		}
	}

	currentFile := 0

	for _, file := range reader.File {
		path := filepath.Join(destPath, file.Name)

		if file.FileInfo().IsDir() {
			os.MkdirAll(path, os.ModePerm)
			continue
		}

		if err := os.MkdirAll(filepath.Dir(path), os.ModePerm); err != nil {
			return err
		}

		outFile, err := os.OpenFile(path, os.O_WRONLY|os.O_CREATE|os.O_TRUNC, file.Mode())
		if err != nil {
			return err
		}

		rc, err := file.Open()
		if err != nil {
			outFile.Close()
			return err
		}

		currentFile++
		percentage := float64(currentFile) * 100 / float64(totalFiles)
		fmt.Printf("\r正在解压文件: %d/%d (%.0f%%)", currentFile, totalFiles, percentage)

		_, err = io.Copy(outFile, rc)
		outFile.Close()
		rc.Close()
		if err != nil {
			return err
		}
	}
	fmt.Println()
	return nil
}
