package ipa

import (
	"fmt"
	"io"
	"onetools/cmd/utility"
	"os"
	"path/filepath"
	"strings"
)

// addFilesToApp 从指定文件夹添加文件到.app目录
func addFilesToApp(appPath string, addFilesPath string) error {
	if addFilesPath == "" {
		return nil
	}

	// 检查源文件夹是否存在
	if !utility.IsExist(addFilesPath) {
		logWarning("要添加的文件夹不存在，跳过添加文件: %s", addFilesPath)
		return nil
	}

	if !utility.IsDir(addFilesPath) {
		return fmt.Errorf("指定的路径不是文件夹: %s", addFilesPath)
	}

	logInfo("开始添加文件从: %s 到 %s", addFilesPath, appPath)

	// 遍历源文件夹中的所有文件和文件夹
	err := filepath.Walk(addFilesPath, func(srcPath string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// 跳过根目录本身
		if srcPath == addFilesPath {
			return nil
		}

		// 计算相对路径
		relPath, err := filepath.Rel(addFilesPath, srcPath)
		if err != nil {
			return fmt.Errorf("计算相对路径失败: %w", err)
		}

		// 构建目标路径
		targetPath := filepath.Join(appPath, relPath)

		if info.IsDir() {
			return handleDirectoryAdd(srcPath, targetPath, relPath)
		} else {
			return handleFileAdd(srcPath, targetPath, relPath)
		}
	})

	if err != nil {
		return fmt.Errorf("添加文件失败: %w", err)
	}

	logSuccess("文件添加完成")
	return nil
}

// handleDirectoryAdd 处理文件夹添加
func handleDirectoryAdd(srcPath, targetPath, relPath string) error {
	// 检查是否为特殊文件夹类型
	if isSpecialBundle(relPath) {
		return handleSpecialBundle(srcPath, targetPath, relPath)
	}

	// 普通文件夹处理
	if utility.IsExist(targetPath) {
		// 目标文件夹已存在，不需要特殊处理，继续遍历内部文件
		logInfo("目标文件夹已存在，将遍历内部文件: %s", relPath)
		return nil
	} else {
		// 目标文件夹不存在，创建文件夹
		if err := os.MkdirAll(targetPath, 0755); err != nil {
			return fmt.Errorf("创建文件夹失败: %s, 错误: %w", relPath, err)
		}
		logInfo("添加文件夹: %s -> %s", srcPath, targetPath)
		return nil
	}
}

// handleFileAdd 处理文件添加
func handleFileAdd(srcPath, targetPath, relPath string) error {
	// 检查目标文件是否已存在
	if utility.IsExist(targetPath) {
		// 文件已存在，强制覆盖
		if err := copyFileForAdd(srcPath, targetPath); err != nil {
			return fmt.Errorf("覆盖文件失败: %s, 错误: %w", relPath, err)
		}
		logInfo("覆盖文件: %s -> %s", srcPath, targetPath)
	} else {
		// 文件不存在，直接复制
		// 确保目标目录存在
		targetDir := filepath.Dir(targetPath)
		if !utility.IsExist(targetDir) {
			if err := os.MkdirAll(targetDir, 0755); err != nil {
				return fmt.Errorf("创建目标目录失败: %s, 错误: %w", targetDir, err)
			}
		}

		if err := copyFileForAdd(srcPath, targetPath); err != nil {
			return fmt.Errorf("添加文件失败: %s, 错误: %w", relPath, err)
		}
		logInfo("添加文件: %s -> %s", srcPath, targetPath)
	}
	return nil
}

// isSpecialBundle 检查是否为特殊文件夹类型
func isSpecialBundle(path string) bool {
	// 获取文件夹名称
	name := filepath.Base(path)

	// 检查是否以特殊后缀结尾
	specialSuffixes := []string{".bundle", ".framework", ".lproj"}
	for _, suffix := range specialSuffixes {
		if strings.HasSuffix(name, suffix) {
			return true
		}
	}
	return false
}

// handleSpecialBundle 处理特殊文件夹类型（.bundle、.framework、.lproj）
func handleSpecialBundle(srcPath, targetPath, relPath string) error {
	// 对于特殊文件夹，直接整体替换
	if utility.IsExist(targetPath) {
		// 删除现有的特殊文件夹
		if err := os.RemoveAll(targetPath); err != nil {
			return fmt.Errorf("删除现有特殊文件夹失败: %s, 错误: %w", relPath, err)
		}
	}

	// 确保目标目录的父目录存在
	targetDir := filepath.Dir(targetPath)
	if !utility.IsExist(targetDir) {
		if err := os.MkdirAll(targetDir, 0755); err != nil {
			return fmt.Errorf("创建目标目录失败: %s, 错误: %w", targetDir, err)
		}
	}

	// 复制整个特殊文件夹
	if err := copyDirRecursivelyForAdd(srcPath, targetPath); err != nil {
		return fmt.Errorf("复制特殊文件夹失败: %s, 错误: %w", relPath, err)
	}

	logInfo("替换特殊文件夹: %s -> %s", srcPath, targetPath)

	// 返回 filepath.SkipDir 来跳过遍历这个特殊文件夹的内部文件
	return filepath.SkipDir
}

// copyFileForAdd 复制文件（用于添加文件功能）
func copyFileForAdd(src, dst string) error {
	srcFile, err := os.Open(src)
	if err != nil {
		return err
	}
	defer srcFile.Close()

	dstFile, err := os.Create(dst)
	if err != nil {
		return err
	}
	defer dstFile.Close()

	_, err = io.Copy(dstFile, srcFile)
	if err != nil {
		return err
	}

	// 复制文件权限
	srcInfo, err := os.Stat(src)
	if err != nil {
		return err
	}

	return os.Chmod(dst, srcInfo.Mode())
}

// copyDirRecursivelyForAdd 递归复制目录（用于添加文件功能）
func copyDirRecursivelyForAdd(src, dst string) error {
	srcInfo, err := os.Stat(src)
	if err != nil {
		return err
	}

	// 创建目标目录
	if err := os.MkdirAll(dst, srcInfo.Mode()); err != nil {
		return err
	}

	entries, err := os.ReadDir(src)
	if err != nil {
		return err
	}

	for _, entry := range entries {
		srcPath := filepath.Join(src, entry.Name())
		dstPath := filepath.Join(dst, entry.Name())

		if entry.IsDir() {
			if err := copyDirRecursivelyForAdd(srcPath, dstPath); err != nil {
				return err
			}
		} else {
			if err := copyFileForAdd(srcPath, dstPath); err != nil {
				return err
			}
		}
	}

	return nil
}
