/*
Copyright © 2022 NAME HERE <EMAIL ADDRESS>
*/
package cmd

import (
	"bufio"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"onetools/cmd/utility"
	"os"
	"path/filepath"
	"regexp"
	"runtime"
	"sort"
	"strings"

	"github.com/spf13/cobra"
)

// 定义插件信息结构体
type Plugin struct {
	Name       string `json:"name"`
	Version    string `json:"version"`
	GradleInfo string `json:"gradleInfo"`
	Enable     bool   `json:"enable"`
}

// 定义原生 SDK 信息结构体
type NativeSDK struct {
	Name    string `json:"name"`
	Version string `json:"version"`
}

// 定义根结构体
type ProjectInfo struct {
	Name          string      `json:"name"`
	Platform      string      `json:"platform"`
	Configuration string      `json:"configuration"`
	Type          string      `json:"type"`
	Engine        string      `json:"engine"`
	EngineVer     string      `json:"engineVer"`
	Plugins       []Plugin    `json:"plugins"`
	NativeSDKs    []NativeSDK `json:"native"`
}

var projectInfo = ProjectInfo{}

// sdkversionCmd represents the version command
var sdkversionCmd = &cobra.Command{
	Use:   "sdkversion",
	Short: "查看接入的sdk(plugins) 版本号",
	Long:  `查看接入的sdk(plugins) 版本号`,
	Run: func(cmd *cobra.Command, args []string) {
		//打印SDK版本号
		showSDKVersion(cmd)
	},
}

func init() {
	rootCmd.AddCommand(sdkversionCmd)

	sdkversionCmd.Flags().StringP("engine-dir", "e", "", "【optional】Path to Unreal Engine directory (e.g.:/Users/<USER>/Epic Games/UE_4.27/Engine)")
	sdkversionCmd.Flags().StringP("project-dir", "d", "", "【optional】Path to project directory,The default is the current command execution directory")
	sdkversionCmd.Flags().StringP("target-name", "n", "", "【optional】Project Target Names")
	sdkversionCmd.Flags().StringP("target-platform", "p", "", "【optional】Build Target Platform (e.g. Windows, Mac, Android、IOS)")
	sdkversionCmd.Flags().StringP("target-configuration", "c", "", "【optional】Build Target configuration (e.g. DebugGame, Development、Shipping)")
	sdkversionCmd.Flags().StringP("target-type", "t", "", "【optional】Build Target type (e.g. Game, Editor)")
	sdkversionCmd.Flags().StringP("project-file", "f", "", "【optional】Path to the project file (.uproject)")
}

func showSDKVersion(cmd *cobra.Command) {
	projectDir, _ := cmd.Flags().GetString("project-dir")
	if projectDir == "" {
		pwdPath, _ := os.Getwd()
		projectDir = pwdPath
	}

	if !utility.IsExist(projectDir) {
		panic(fmt.Sprintf("【%s】路径不存在，请确认project路径是否正确", projectDir))
	}
	// 打印插件版本号
	isUEProject, uprojectName := utility.IsUEProjectName(projectDir)
	if isUEProject {
		unrealEngineProjectVersion(cmd, projectDir, uprojectName)
	} else {
		// unity项目插件打印，ios_sdk_dependency.text里包含了所有信息，暂不需要打印其他
	}

	//打印 native sdk 版本号
	getNativeSDKVersion(cmd, projectDir)

	// 将projectInfo转为 JSON 数据
	jsonData, err := json.MarshalIndent(projectInfo, "", "  ")
	if err != nil {
		fmt.Println("Error converting to JSON:", err)
		return
	}

	// 输出 JSON 数据
	fmt.Println(string(jsonData))
}

func unrealEngineProjectVersion(cmd *cobra.Command, projectDir string, uprojectName string) {
	// 虚幻引擎版本号打印
	projectInfo.Engine = "unreal"
	engineDir, _ := cmd.Flags().GetString("engine-dir")
	if engineDir != "" {
		engineVersion := utility.GetUnrealEngineVersion(engineDir)
		projectInfo.EngineVer = engineVersion
		//fmt.Printf("UE引擎版本号:%s\n", engineVersion)
	}

	// 打印TargetName
	targetName, _ := cmd.Flags().GetString("target-name")
	if targetName != "" {
		projectInfo.Name = targetName
		//fmt.Printf("TargetName:%s\n", targetName)
	} else {
		projectInfo.Name = uprojectName
	}

	// 打印TargetPlatform
	targetPlatform, _ := cmd.Flags().GetString("target-platform")
	if targetPlatform != "" {
		projectInfo.Platform = targetPlatform
		//fmt.Printf("TargetPlatform:%s\n", targetPlatform)
	}

	// 打印TargetConfiguration
	targetConfiguration, _ := cmd.Flags().GetString("target-configuration")
	if targetConfiguration != "" {
		projectInfo.Configuration = targetConfiguration
		//fmt.Printf("TargetConfiguration:%s\n", targetConfiguration)
	}

	// 打印TargetType
	targetType, _ := cmd.Flags().GetString("target-type")
	if targetType != "" {
		projectInfo.Type = targetType
		//fmt.Printf("TargetType:%s\n", targetType)
	}

	// 虚幻项目插件打印
	scanPath := filepath.Join(projectDir, "Plugins")
	if !utility.IsExist(scanPath) {
		return
	}
	//fmt.Printf("项目中包含的插件版本号:\n")
	wmAllPlugins := wmUnrealEnginPlugins()
	unenabledPlugins := utility.UEProjectUnenabledPlugins(projectDir)

	utility.GetFilesFromDir(scanPath, func(filePath string) {
		if strings.HasSuffix(filePath, ".uplugin") {
			content, err := ioutil.ReadFile(filePath)
			if err != nil {
				fmt.Printf("【%s】读取文件失败: 【%s】\n", filePath, err)
			}
			projectMap := make(map[string]interface{})
			err = json.Unmarshal(content, &projectMap)
			if err != nil {
				fmt.Printf("【%s】解析文件失败: 【%s】\n", filePath, err)
			} else {
				friendlyName := projectMap["FriendlyName"].(string)
				versionName := projectMap["VersionName"].(string)
				if utility.IsContain(wmAllPlugins, friendlyName) {
					pluginInfo := Plugin{
						Name:    friendlyName,
						Version: versionName,
					}

					// 是我们的插件
					if !utility.IsContain(unenabledPlugins, friendlyName) {
						// 插件已启用
						//fmt.Printf("已启用【%s】:%s\n", friendlyName, versionName)
						pluginInfo.Enable = true
					} else {
						// 插件未启用
						pluginInfo.Enable = false
						//fmt.Printf("未启用【%s】:%s\n", friendlyName, versionName)
					}
					gradleData := getSDKGradleFileInfo(filePath, targetPlatform)
					pluginInfo.GradleInfo = gradleData
					// 添加到projectInfo.Plugins
					projectInfo.Plugins = append(projectInfo.Plugins, pluginInfo)
				}
			}
		}
	})
}

func getNativeSDKVersion(cmd *cobra.Command, projectDir string) {
	targetPlatform, _ := cmd.Flags().GetString("target-platform")
	targetPlatformLower := strings.ToLower(targetPlatform)
	projectInfo.NativeSDKs = []NativeSDK{} //初始化，防止转成json后为null

	if targetPlatformLower != "ios" && targetPlatformLower != "mac" {
		// 当前只有iOS 和 mac 有对应的SDK版本号
		return
	}
	nativeSDKPath := utility.GetDownloadNativeSDKPath(projectDir)
	sdkVersionFile := filepath.Join(nativeSDKPath, "ios_sdk_dependency.txt")
	if utility.IsExist(sdkVersionFile) {
		//fmt.Printf("\n项目中包含的Native SDK版本号:\n")
		f, _ := os.Open(sdkVersionFile)
		defer f.Close()
		r := bufio.NewReader(f)
		sdkVersions := make(map[string]string)
		var sdkAllKeys []string
		for {
			lineText, err := readLine(r)
			if err != nil {
				break
			}
			var lineContent = lineText
			if strings.Contains(lineText, "->") || !strings.Contains(lineText, targetPlatformLower) {
				continue
			} else {
				re := regexp.MustCompile(`\s*\(\*\)`) //删除(*)
				lineContent = re.ReplaceAllString(lineContent, "")
			}
			contentArray := strings.Split(lineContent, ":")
			if len(contentArray) > 2 && !utility.IsContain(sdkAllKeys, contentArray[1]) {
				sdkVersions[contentArray[1]] = contentArray[2]
				sdkAllKeys = append(sdkAllKeys, contentArray[1])
			}
		}
		sort.Strings(sdkAllKeys)
		for _, sdkName := range sdkAllKeys {
			nativeInfo := NativeSDK{
				Name:    sdkName,
				Version: sdkVersions[sdkName],
			}
			projectInfo.NativeSDKs = append(projectInfo.NativeSDKs, nativeInfo)
			//fmt.Printf("[%s]:%s\n", sdkName, sdkVersions[sdkName])
		}
	}
}
func wmUnrealEnginPlugins() []string {
	defaultPlugins := utility.UESupportPlugins()
	extendPlugins := []string{"OneEngineSDK", "DependLibraries", "IOSConfig"}
	wmAllPlugins := append(defaultPlugins, extendPlugins...)
	return wmAllPlugins
}

func getSDKGradleFileInfo(upluginPath string, targetPlatform string) string {
	// 获取文件的目录路径
	upluginDir := filepath.Dir(upluginPath)
	targetPlatformLower := strings.ToLower(targetPlatform)
	var gradleFiles []string
	var walkErr error
	if targetPlatformLower == "ios" {
		walkErr = filepath.Walk(upluginDir, visit("[Ii]OS", &gradleFiles))
	} else if targetPlatformLower == "mac" {
		walkErr = filepath.Walk(upluginDir, visit("[Mm]acOS", &gradleFiles))
	} else if targetPlatformLower == "android" {
		walkErr = filepath.Walk(upluginDir, visit("[Aa]ndroid", &gradleFiles))
	}
	if walkErr != nil {
		fmt.Printf("【%s】目录扫描gradle文件失败: 【%s】\n", upluginDir, walkErr)
	}
	var allGradleInfo []string
	for _, gradleFilePath := range gradleFiles {
		content := utility.GetFileContent(gradleFilePath)
		// 匹配 dependencies 块中的内容
		dependenciesData, _ := extractAllDependenciesBlocks(content)
		if len(dependenciesData) > 0 {
			allGradleInfo = append(allGradleInfo, dependenciesData...)
		}
	}

	if len(allGradleInfo) > 0 {
		allGradleStr := strings.Join(allGradleInfo, "\n")
		return allGradleStr
	} else {
		return ""
	}
}

func visit(regexStr string, files *[]string) filepath.WalkFunc {
	platformFlag := "/"
	if runtime.GOOS == "windows" {
		platformFlag = "\\"
	}
	fullRegexStr := platformFlag + regexStr + platformFlag
	var regex *regexp.Regexp = regexp.MustCompile(fullRegexStr)
	return func(path string, info os.FileInfo, err error) error {
		if err == nil && !info.IsDir() {
			if regex.MatchString(path) && strings.HasSuffix(path, ".gradle") {
				*files = append(*files, path)
			}
		}
		return nil
	}
}

func readLine(r *bufio.Reader) (string, error) {
	line, isprefix, err := r.ReadLine()
	for isprefix && err == nil {
		var bs []byte
		bs, isprefix, err = r.ReadLine()
		line = append(line, bs...)
	}
	return string(line), err
}

// 提取所有的 dependencies 块
func extractAllDependenciesBlocks(content string) ([]string, error) {
	var dependenciesBlocks []string

	// 使用正则表达式找到所有包含 "dependencies {" 的行
	re := regexp.MustCompile(`^\s*dependencies\s*\{`)
	lines := strings.Split(content, "\n")

	for i, line := range lines {
		if re.MatchString(line) {
			startIndex := i
			endIndex, err := findClosingBraceIndex(lines, startIndex)
			if err != nil {
				return nil, err
			}
			// 提取 dependencies 块并移除空行和多余的空白字符
			block := strings.Join(lines[startIndex:endIndex+1], "\n")
			cleanedBlock := removeEmptyLinesAndTrimSpaces(block)
			dependenciesBlocks = append(dependenciesBlocks, cleanedBlock)
		}
	}
	return dependenciesBlocks, nil
}

// 查找闭合的花括号的位置
func findClosingBraceIndex(lines []string, startIndex int) (int, error) {
	openBraces := 1
	for i := startIndex + 1; i < len(lines); i++ {
		line := lines[i]
		openBraces += strings.Count(line, "{")
		openBraces -= strings.Count(line, "}")

		if openBraces == 0 {
			return i, nil
		}
	}
	return -1, fmt.Errorf("unmatched closing brace")
}

// 移除空行、多余的空白字符和注释
func removeEmptyLinesAndTrimSpaces(block string) string {
	lines := strings.Split(block, "\n")
	var nonEmptyLines []string

	for _, line := range lines {
		if trimmedLine := strings.TrimSpace(line); trimmedLine != "" && strings.HasPrefix(trimmedLine, "//") == false {
			nonEmptyLines = append(nonEmptyLines, trimmedLine)
		}
	}

	return strings.Join(nonEmptyLines, "\n")
}
