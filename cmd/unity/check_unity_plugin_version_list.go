package unity

/*
go run check_unity_plugin_version_list.go -plugin=GlobalSDK -env=ios-snapshot
查询maven有哪些可用的版本
*/

import (
	"encoding/xml"
	"fmt"
	"io/ioutil"
	"net/http"
)

type Metadata struct {
	Versioning struct {
		Versions struct {
			Version []string `xml:"version"`
		} `xml:"versions"`
	} `xml:"versioning"`
}

func getMetadataUrl(pluginName, environment string) string {
	return fmt.Sprintf("http://nexus.sys.wanmei.com/repository/%s/com/pwrd/u3d/%s/maven-metadata.xml", environment, pluginName)
}

// CheckUnityPluginVersionList 查询maven可用版本
func CheckUnityPluginVersionList(pluginName, environment string) ([]string, error) {
	if pluginName == "" {
		return nil, fmt.Errorf("请使用 --plugin 指定插件名")
	}
	url := getMetadataUrl(pluginName, environment)
	resp, err := http.Get(url)
	if err != nil {
		return nil, fmt.Errorf("下载maven-metadata.xml失败: %v", err)
	}
	defer resp.Body.Close()
	if resp.StatusCode != 200 {
		return nil, fmt.Errorf("请求失败，状态码: %d", resp.StatusCode)
	}
	data, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取maven-metadata.xml失败: %v", err)
	}
	var meta Metadata
	err = xml.Unmarshal(data, &meta)
	if err != nil {
		return nil, fmt.Errorf("解析maven-metadata.xml失败: %v", err)
	}
	return meta.Versioning.Versions.Version, nil
}

// 删除 main 函数，已由 CheckUnityPluginVersionList 替代
