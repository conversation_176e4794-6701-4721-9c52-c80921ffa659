package unity

/*
查询可用的unitypackage包，传入nexus路径，输出一级目录名
go run check_unity_name_list.go ios-snapshot/com/pwrd/u3d
*/

import (
	"fmt"
	"io/ioutil"
	"net/http"
	"regexp"
)

// CheckUnityNameList 查询可用的unitypackage包，传入nexus路径，输出一级目录名
func CheckUnityNameList(nexusPath string) ([]string, error) {
	if nexusPath == "" {
		return nil, fmt.Errorf("nexusPath不能为空")
	}
	url := "https://nexus.sys.wanmei.com/service/rest/repository/browse/" + nexusPath
	resp, err := http.Get(url)
	if err != nil {
		return nil, fmt.Errorf("Error fetching URL: %v", err)
	}
	defer resp.Body.Close()

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("Error reading response: %v", err)
	}

	re := regexp.MustCompile(`<a href=\"([^\"]+)/\">([^<]+)</a>`)
	matches := re.FindAllStringSubmatch(string(body), -1)
	var result []string
	for _, match := range matches {
		if match[1] != ".." {
			result = append(result, match[1])
		}
	}
	return result, nil
}
