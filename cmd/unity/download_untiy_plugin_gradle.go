package unity

import (
	"archive/zip"
	"fmt"
	"io"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
)

// DownloadUnityPluginGradle 下载Unity插件并返回unitypackage路径
func DownloadUnityPluginGradle(gradlePath, groupId, artifactId, version, destPath string) (string, error) {
	if gradlePath == "" || groupId == "" || artifactId == "" || version == "" || destPath == "" {
		return "", fmt.Errorf("参数不能为空: gradle_path, group_id, artifact_id, version, dest_path")
	}
	fmt.Println("[1/4] 生成临时Gradle工程...")
	tmpDir, err := os.MkdirTemp("", "gradle_download_*")
	if err != nil {
		return "", err
	}
	defer os.RemoveAll(tmpDir)

	gradleContent := getGradleContent(groupId, artifactId, version)
	gradleFile := filepath.Join(tmpDir, "build.gradle")
	err = os.WriteFile(gradleFile, []byte(gradleContent), 0644)
	if err != nil {
		return "", err
	}

	// 打印gradle文件
	fmt.Println("生成的Gradle文件内容:")
	fmt.Println(gradleContent)
	fmt.Println("临时Gradle工程路径:", tmpDir)
	fmt.Println("Gradle文件路径:", gradleFile)

	fmt.Println("[2/4] 执行Gradle下载...")
	needReload := false
	if strings.Contains(version, "-SNAPSHOT") {
		needReload = true
	}

	var cmd *exec.Cmd
	if needReload {
		cmd = exec.Command(gradlePath, "download", "--refresh-dependencies")
	} else {
		cmd = exec.Command(gradlePath, "download")
	}
	cmd.Dir = tmpDir
	cmd.Stdout = nil // 不输出任何gradle日志
	cmd.Stderr = nil
	err = cmd.Run()
	if err != nil {
		return "", err
	}

	fmt.Println("[3/4] 查找下载的zip...")
	var foundZip string
	err = filepath.Walk(tmpDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		if !info.IsDir() && strings.HasSuffix(info.Name(), ".zip") {
			foundZip = path
			return io.EOF // 终止遍历
		}
		return nil
	})
	if foundZip == "" {
		return "", fmt.Errorf("未找到zip文件")
	}

	fmt.Println("[4/4] 拷贝zip到目标路径:", destPath)
	// 增加下载进度打印
	srcFile, err := os.Open(foundZip)
	if err != nil {
		return "", fmt.Errorf("打开源zip失败: %v", err)
	}
	defer srcFile.Close()
	dstDir := filepath.Dir(destPath)
	if err := os.MkdirAll(dstDir, 0755); err != nil {
		return "", err
	}
	dstFile, err := os.Create(destPath)
	if err != nil {
		return "", err
	}
	defer dstFile.Close()
	stat, err := srcFile.Stat()
	if err != nil {
		return "", err
	}
	size := stat.Size()
	var written int64
	buf := make([]byte, 32*1024)
	for {
		n, err := srcFile.Read(buf)
		if n > 0 {
			wn, werr := dstFile.Write(buf[:n])
			written += int64(wn)
			if werr != nil {
				return "", fmt.Errorf("写入目标zip失败: %v", werr)
			}
			if size > 0 {
				percent := float64(written) / float64(size) * 100
				fmt.Printf("\r拷贝进度: %.1f%% (%d/%d bytes)", percent, written, size)
			} else {
				fmt.Printf("\r已拷贝: %d bytes", written)
			}
		}
		if err == io.EOF {
			break
		}
		if err != nil {
			return "", fmt.Errorf("拷贝zip失败: %v", err)
		}
	}
	fmt.Println("\n拷贝完成。")

	fmt.Println("解压zip并查找unitypackage...")
	unityPkgPath, err := unzipAndFindUnityPackageWithProgress(destPath)
	if err != nil {
		return "", err
	}
	if unityPkgPath == "" {
		return "", fmt.Errorf("未找到unitypackage文件")
	}
	// 只输出unitypackage路径
	return unityPkgPath, nil
}

func getGradleContent(groupId, artifactId, version string) string {
	return fmt.Sprintf(`apply plugin: 'java'
apply plugin: 'java-library'

repositories {
	maven {
		url "http://nexus.sys.wanmei.com/repository/maven-public/"
		allowInsecureProtocol = true
	}
}

dependencies {
	api "%s:%s:%s:SDK@zip"
}

configurations {
	customConfig.extendsFrom api
}

task download(type: Copy) {
	from(project.configurations.customConfig) {
		include '*.zip'
	}
	into '.'
}
`, groupId, artifactId, version)
}

func unzipAndFindUnityPackageWithProgress(zipPath string) (string, error) {
	fmt.Println("开始解压zip:", zipPath)
	unzipDir := zipPath + "_unzip"
	err := os.MkdirAll(unzipDir, 0755)
	if err != nil {
		return "", err
	}

	r, err := zip.OpenReader(zipPath)
	if err != nil {
		return "", err
	}
	defer r.Close()
	total := len(r.File)
	for i, f := range r.File {
		fpath := filepath.Join(unzipDir, f.Name)
		if f.FileInfo().IsDir() {
			os.MkdirAll(fpath, f.Mode())
			continue
		}
		if err := os.MkdirAll(filepath.Dir(fpath), 0755); err != nil {
			return "", err
		}
		outFile, err := os.OpenFile(fpath, os.O_WRONLY|os.O_CREATE|os.O_TRUNC, f.Mode())
		if err != nil {
			return "", err
		}
		rc, err := f.Open()
		if err != nil {
			outFile.Close()
			return "", err
		}
		_, err = io.Copy(outFile, rc)
		outFile.Close()
		rc.Close()
		if err != nil {
			return "", err
		}
		if (i+1)%20 == 0 || i == total-1 {
			fmt.Printf("\r解压进度: %d/%d 文件", i+1, total)
		}
	}
	fmt.Println("\n解压完成，查找unitypackage文件...")

	// 查找unitypackage文件
	var unityPkg string
	err = filepath.Walk(unzipDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		if !info.IsDir() && strings.HasSuffix(info.Name(), ".unitypackage") {
			unityPkg = path
			return io.EOF // 终止遍历
		}
		return nil
	})
	if err != nil && err != io.EOF {
		return "", err
	}
	return unityPkg, nil
}
