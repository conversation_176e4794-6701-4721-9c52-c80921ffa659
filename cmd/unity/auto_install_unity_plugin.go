package unity

import (
	"fmt"
	"io/ioutil"
	"os"
	"strings"
)

// AutoInstallUnityPlugin 用于自动下载并安装指定Unity插件
func AutoInstallUnityPlugin(sdkName, targetVersion, targetProject string) error {
	if sdkName == "" || targetVersion == "" || targetProject == "" {
		return fmt.Errorf("参数不能为空: sdkName, targetVersion, targetProject")
	}

	// 判断查找plugin的位置，如果targetVersion包含"-SNAPSHOT"则查找ios-snapshot，否则查找ios-release
	checkEnv := "ios-release"
	if strings.Contains(targetVersion, "-SNAPSHOT") {
		checkEnv = "ios-snapshot"
	} else {
		checkEnv = "ios-release"
	}

	// 1. 查询所有可用版本（只查 ios-snapshot）
	versions, err := CheckUnityPluginVersionList(sdkName, checkEnv)
	if err != nil {
		return fmt.Errorf("查询可用版本失败: %v", err)
	}
	if len(versions) == 0 {
		return fmt.Errorf("未找到可用版本")
	}

	// 2. 只下载目标版本（可扩展为全部）
	var matchedVersion string
	for _, v := range versions {
		if strings.HasPrefix(v, targetVersion) {
			matchedVersion = v
			break
		}
	}
	if matchedVersion == "" {
		return fmt.Errorf("未找到目标版本: %s", targetVersion)
	}

	// 获取 gradle 路径
	gradlePath := ""
	if os.Getenv("GradlePath") != "" {
		gradlePath = os.Getenv("GradlePath")
	} else {
		dir, _ := os.Executable()
		exePath := dir
		if idx := strings.LastIndex(dir, string(os.PathSeparator)); idx != -1 {
			exePath = dir[:idx]
		}
		gradleDir := exePath + string(os.PathSeparator) + "Gradle"
		// 判断操作系统
		if strings.Contains(strings.ToLower(os.Getenv("OS")), "windows") || strings.HasSuffix(strings.ToLower(os.Args[0]), ".exe") {
			gradlePath = gradleDir + string(os.PathSeparator) + "gradlew.bat"
		} else {
			gradlePath = gradleDir + string(os.PathSeparator) + "gradlew"
		}
	}
	if _, err := os.Stat(gradlePath); os.IsNotExist(err) {
		return fmt.Errorf("gradle路径不存在: %s", gradlePath)
	}

	tmpZip := "./tmp2/" + sdkName + ".zip"
	os.MkdirAll("./tmp2", 0755)
	unityPkgPath, err := DownloadUnityPluginGradle(
		gradlePath,
		"com.pwrd.u3d",
		sdkName,
		matchedVersion,
		tmpZip,
	)
	if err != nil {
		os.RemoveAll("./tmp2")
		return fmt.Errorf("下载插件失败: %v", err)
	}
	if unityPkgPath == "" {
		return fmt.Errorf("未找到 unitypackage 路径")
	}

	files, _ := ioutil.ReadDir(targetProject)
	for _, f := range files {
		if !f.IsDir() && strings.HasPrefix(f.Name(), "unitypackage_filelist_") && strings.Contains(f.Name(), sdkName) && strings.HasSuffix(f.Name(), ".txt") {
			parts := strings.Split(f.Name(), "_")
			if len(parts) >= 4 {
				verWithExt := parts[len(parts)-1]
				version := strings.TrimSuffix(verWithExt, ".txt")
				fmt.Printf("检测到旧版本，准备卸载: %s %s\n", sdkName, version)
				_ = UninstallUnityPackageTxt(targetProject, sdkName, version)
			}
		}
	}

	err = InstallUnityPackageDir(targetProject, unityPkgPath, sdkName, targetVersion)
	if err != nil {
		return fmt.Errorf("安装失败: %v", err)
	}

	os.RemoveAll("./tmp2")
	fmt.Println("全部流程完成！")
	return nil
}
