package unity

/*
go run uninstall_unity_package_txt.go -project /Users/<USER>/Desktop/demo20250529001/ -sdk GlobalSDK -version 3.30.0.0
*/
import (
	"bufio"
	"fmt"
	"os"
	"path/filepath"
	"sort"
	"strings"
)

// UninstallUnityPackageTxt 卸载指定unitypackage版本
func UninstallUnityPackageTxt(unityProjectPath, sdkName, sdkVersion string) error {
	if unityProjectPath == "" || sdkName == "" || sdkVersion == "" {
		return fmt.Errorf("参数不能为空: project, sdk, version")
	}
	fileListPath := filepath.Join(unityProjectPath, fmt.Sprintf("unitypackage_filelist_%s_%s.txt", sdkName, sdkVersion))
	file, err := os.Open(fileListPath)
	if err != nil {
		return fmt.Errorf("无法打开文件列表: %v", err)
	}
	defer file.Close()

	var relPaths []string
	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		line := scanner.Text()
		// 跳过注释和空行
		if strings.HasPrefix(line, "//") || strings.TrimSpace(line) == "" {
			continue
		}
		// 支持一行多个文件用空格分隔
		for _, p := range strings.Fields(line) {
			relPaths = append(relPaths, p)
		}
	}
	if err := scanner.Err(); err != nil {
		return fmt.Errorf("读取文件列表出错: %v", err)
	}

	// 直接使用原始文件路径
	normRelPaths := relPaths

	// 记录所有涉及的目录
	dirsSet := make(map[string]struct{})
	for _, rel := range normRelPaths {
		dir := filepath.Dir(rel)
		for dir != "." && dir != "" {
			dirsSet[dir] = struct{}{}
			dir = filepath.Dir(dir)
		}
	}

	fmt.Println("将要删除的文件数量:", len(normRelPaths))
	for _, rel := range normRelPaths {
		fmt.Println("待删除:", filepath.Join(unityProjectPath, rel))
	}

	// 删除文件和对应.meta
	for _, rel := range normRelPaths {
		target := filepath.Join(unityProjectPath, rel)
		if _, err := os.Stat(target); err == nil {
			os.Remove(target)
			fmt.Println("删除:", target)
			metaFile := target + ".meta"
			if _, err := os.Stat(metaFile); err == nil {
				os.Remove(metaFile)
				fmt.Println("删除:", metaFile)
			}
		}
	}

	// 自底向上删除空目录及其.meta
	// 先将dirsSet转为slice
	var dirs []string
	for dir := range dirsSet {
		dirs = append(dirs, dir)
	}
	sort.Slice(dirs, func(i, j int) bool {
		return strings.Count(dirs[i], string(os.PathSeparator)) > strings.Count(dirs[j], string(os.PathSeparator))
	})
	// 拼接unityProjectPath，保证查找的是完整路径
	for _, relDir := range dirs {
		dirPath := filepath.Join(unityProjectPath, relDir)
		entries, err := os.ReadDir(dirPath)
		if err != nil {
			continue
		}
		hasRealFile := false
		for _, entry := range entries {
			if !entry.IsDir() && !strings.HasSuffix(entry.Name(), ".meta") {
				hasRealFile = true
				break
			}
		}
		if !hasRealFile {
			// 先删掉所有 .meta 文件
			for _, entry := range entries {
				if !entry.IsDir() && strings.HasSuffix(entry.Name(), ".meta") {
					metaPath := filepath.Join(dirPath, entry.Name())
					os.Remove(metaPath)
					fmt.Println("删除:", metaPath)
				}
			}
			os.Remove(dirPath)
			fmt.Println("删除空文件夹:", dirPath)
		}
	}
	// 清理完毕后，删除fileListPath对应的文件
	if err := os.Remove(fileListPath); err == nil {
		fmt.Println("删除文件列表:", fileListPath)
	}
	return nil
}
