package unity

import (
	"fmt"
	"os"
	"strings"
)

// CheckInstallPluginList 查询指定项目已安装的unity插件列表
func CheckInstallPluginList(targetProject string) ([]string, error) {
	files, err := os.ReadDir(targetProject)
	if err != nil {
		return nil, fmt.Errorf("读取项目目录失败: %v", err)
	}
	var installedPlugins []string
	for _, f := range files {
		if !f.IsDir() && strings.HasPrefix(f.Name(), "unitypackage_filelist_") && strings.HasSuffix(f.Name(), ".txt") {
			// 文件名格式: unitypackage_filelist_{sdkName}_{sdkVersion}.txt
			parts := strings.Split(f.Name(), "_")
			if len(parts) >= 4 {
				sdkName := parts[2]
				verWithExt := parts[3]
				version := strings.TrimSuffix(verWithExt, ".txt")
				installedPlugins = append(installedPlugins, fmt.Sprintf("%s %s", sdkName, version))
			}
		}
	}
	return installedPlugins, nil
}

// PrintInstallPluginList 打印指定项目已安装的unity插件列表
func PrintInstallPluginList(targetProject string) {
	plugins, err := CheckInstallPluginList(targetProject)
	if err != nil {
		fmt.Println(err)
		return
	}
	if len(plugins) == 0 {
		fmt.Println("未检测到已安装的unity插件")
		return
	}
	fmt.Println("已安装的unity插件列表:")
	for _, p := range plugins {
		if strings.HasSuffix(p, "-SNAPSHOT") {
			fmt.Println(strings.Replace(p, "-SNAPSHOT", " [env: debug]", 1))
		} else {
			fmt.Println(p)
		}
	}
}
