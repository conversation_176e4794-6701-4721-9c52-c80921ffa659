package unity

/*
go run install_package_dir.go -project /Users/<USER>/Desktop/demo20250529001 -unitypackage tmp2/GlobalSDK.zip_unzip/Assets/Packages/OverSeaUnitySDK_v********.unitypackage -sdk GlobalSDK -version ********
*/
import (
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strings"
)

// InstallUnityPackageDir 用于安装unitypackage到指定工程
func InstallUnityPackageDir(projectPath, unitypackagePath, sdkName, sdkVersion string) error {
	if projectPath == "" || unitypackagePath == "" || sdkName == "" || sdkVersion == "" {
		return fmt.Errorf("参数不能为空: project, unitypackage, sdk, version")
	}

	unitypackageAbs := unitypackagePath
	if !filepath.IsAbs(unitypackageAbs) {
		absPath, err := filepath.Abs(unitypackageAbs)
		if err == nil {
			unitypackageAbs = absPath
		}
	}
	fmt.Printf("unitypackagePath 绝对路径: %s\n", unitypackageAbs)

	// 1. 解压 unitypackage 到缓存目录
	cacheDir := filepath.Join(os.TempDir(), "unitypackage_cache")
	os.RemoveAll(cacheDir)
	os.MkdirAll(cacheDir, 0755)
	err := ExtractUnityPackage(unitypackageAbs, cacheDir)
	if err != nil {
		return fmt.Errorf("extract unitypackage failed: %v", err)
	}

	// 2. 遍历缓存目录，收集所有 Assets 下的文件路径
	var fileList []string
	var unityPkgRoot string
	entries, err := os.ReadDir(cacheDir)
	if err != nil || len(entries) == 0 {
		return fmt.Errorf("no entries found in cache dir or failed to read cache dir: %v", err)
	}
	for _, entry := range entries {
		if entry.IsDir() {
			unityPkgRoot = entry.Name()
			break
		}
	}
	if unityPkgRoot == "" {
		return fmt.Errorf("no root folder found in unitypackage cache dir")
	}

	rootPath := filepath.Join(cacheDir, unityPkgRoot)
	walkErr := filepath.Walk(rootPath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		if !info.IsDir() {
			rel, _ := filepath.Rel(rootPath, path)
			if strings.HasPrefix(rel, "Assets") {
				fileList = append(fileList, rel)
			}
		}
		return nil
	})
	if walkErr != nil {
		return fmt.Errorf("walk cache dir failed: %v", walkErr)
	}

	// 3. 复制文件到目标工程
	for _, relPath := range fileList {
		src := filepath.Join(rootPath, relPath)
		dst := filepath.Join(projectPath, relPath)
		dstDir := filepath.Dir(dst)
		os.MkdirAll(dstDir, 0755)
		err := copyFile(src, dst)
		if err != nil {
			return fmt.Errorf("copy failed: %s -> %s, err: %v", src, dst, err)
		}
	}

	// 4. 写入文件列表，文件名拼接 SDK 和版本号
	listFile := filepath.Join(projectPath, fmt.Sprintf("unitypackage_filelist_%s_%s.txt", sdkName, sdkVersion))
	fileListStr := strings.Join(fileList, "\n")
	writeErr := os.WriteFile(listFile, []byte(fileListStr+"\n"), 0644)
	if writeErr != nil {
		return fmt.Errorf("write file list failed: %v", writeErr)
	}
	fmt.Println("File list written to:", listFile)

	// 5. 删除缓存目录
	os.RemoveAll(cacheDir)
	fmt.Println("Done.")
	return nil
}

// copyFile 复制文件内容
func copyFile(src, dst string) error {
	srcFile, err := os.Open(src)
	if err != nil {
		return err
	}
	defer srcFile.Close()
	dstFile, err := os.Create(dst)
	if err != nil {
		return err
	}
	defer dstFile.Close()
	_, err = io.Copy(dstFile, srcFile)
	return err
}
