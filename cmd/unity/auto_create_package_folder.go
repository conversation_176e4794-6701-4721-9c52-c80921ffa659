package unity

import (
	"encoding/base64"
	"encoding/json"
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"regexp"
)

// 解析 mobileprovision 文件，返回 bundleID, teamID, codeSign, provisioningProfile, method
func extractInfoFromMobileprovision(content string) (bundleID, teamID, codeSign, provisioningProfile, method string) {
	appIDRe := regexp.MustCompile(`<key>application-identifier</key>\s*<string>([^<]+)</string>`)
	teamIDRe := regexp.MustCompile(`<key>com.apple.developer.team-identifier</key>\s*<string>([^<]+)</string>`)
	nameRe := regexp.MustCompile(`<key>Name</key>\s*<string>([^<]+)</string>`)
	// codeSign 默认 Apple Development
	codeSign = "Apple Development"
	// provisioningProfile 默认 bundleID

	bundleID, teamID, provisioningProfile = "", "", ""
	appIDMatch := appIDRe.FindStringSubmatch(content)
	if len(appIDMatch) > 1 {
		parts := regexp.MustCompile(`^[^.]+\.(.+)$`).FindStringSubmatch(appIDMatch[1])
		if len(parts) > 1 {
			bundleID = parts[1]
		}
	}
	teamIDMatch := teamIDRe.FindStringSubmatch(content)
	if len(teamIDMatch) > 1 {
		teamID = teamIDMatch[1]
	}
	nameMatch := nameRe.FindStringSubmatch(content)
	if len(nameMatch) > 1 {
		provisioningProfile = nameMatch[1]
	}

	// 提取证书类型（CERTIFICATES Name）
	certType := ""
	// 1. 尝试提取 <key>Name</key> 后的 <string>Apple ...</string>
	certTypeRe := regexp.MustCompile(`<key>Name</key>\s*<string>(Apple [^<]+)</string>`)
	if match := certTypeRe.FindStringSubmatch(content); len(match) > 1 {
		certType = match[1]
	}
	// 2. 如果没找到，再尝试文本 CERTIFICATES Name 格式
	if certType == "" {
		certNameRe := regexp.MustCompile(`(?m)Name:\s*\nApple [^\n]+`)
		if match := certNameRe.FindString(content); match != "" {
			lines := regexp.MustCompile(`\n`).Split(match, -1)
			if len(lines) > 1 {
				certType = lines[1]
			}
		}
	}
	// 3. 兜底
	if certType == "" {
		certType = "Apple Development"
	}
	codeSign = certType
	if codeSign == "" {
		codeSign = "Apple Development" // fallback
	}

	method = "development" // 默认
	apsEnvRe := regexp.MustCompile(`<key>aps-environment</key>\s*<string>([^<]+)</string>`)
	if match := apsEnvRe.FindStringSubmatch(content); len(match) > 1 {
		if match[1] == "production" {
			// 判断是否有设备列表（Ad Hoc）
			provisionedDevicesRe := regexp.MustCompile(`<key>ProvisionedDevices</key>\s*<array>([\s\S]*?)</array>`)
			if provisionedDevicesRe.MatchString(content) {
				method = "ad-hoc"
			} else {
				method = "app-store"
			}
		} else if match[1] == "development" {
			method = "development"
		}
	}

	// method 推断后，修正 codeSign
	if method == "app-store" || method == "ad-hoc" {
		codeSign = "Apple Distribution"
	}

	return
}

// AutoCreatePackageFolder 自动生成打包文件夹
func AutoCreatePackageFolder(unityProject, region, mobileprovision string) error {
	if unityProject == "" || region == "" || mobileprovision == "" {
		return fmt.Errorf("参数不能为空: unity, region, profile")
	}
	content, err := os.ReadFile(mobileprovision)
	if err != nil {
		return fmt.Errorf("读取mobileprovision失败: %v", err)
	}
	bundleID, teamID, codeSign, provisioningProfile, method := extractInfoFromMobileprovision(string(content))

	// 额外提取完整证书名（如 Apple Development: yanshan liu (25UN2ZP5WW)）
	fullCertName := ""
	fullCertRe := regexp.MustCompile(`<key>Name</key>\s*<string>(Apple (Development|Distribution):[^<]+)</string>`)
	if match := fullCertRe.FindStringSubmatch(string(content)); len(match) > 1 {
		fullCertName = match[1]
	}
	if fullCertName == "" {
		// 尝试解析 DeveloperCertificates base64
		devCertsRe := regexp.MustCompile(`<key>DeveloperCertificates</key>\s*<array>\s*<data>([A-Za-z0-9+/=\s]+)</data>`)
		if match := devCertsRe.FindStringSubmatch(string(content)); len(match) > 1 {
			certBase64 := match[1]
			certBase64 = regexp.MustCompile(`\s+`).ReplaceAllString(certBase64, "")
			// 写入临时文件
			tmpFile, err := os.CreateTemp("", "cert-*.der")
			if err == nil {
				defer os.Remove(tmpFile.Name())
				certBytes, _ := base64.StdEncoding.DecodeString(certBase64)
				tmpFile.Write(certBytes)
				tmpFile.Close()
				// 用 openssl 解析
				cmd := exec.Command("openssl", "x509", "-inform", "der", "-in", tmpFile.Name(), "-noout", "-subject")
				out, err := cmd.CombinedOutput()
				if err == nil {
					cnRe := regexp.MustCompile(`CN=([^,]+)`)
					if match := cnRe.FindStringSubmatch(string(out)); len(match) > 1 {
						fullCertName = match[1]
					}
				}
			}
		}
	}
	if fullCertName == "" {
		// 尝试文本格式
		certNameRe := regexp.MustCompile(`(?m)Name:\s*\n(Apple (Development|Distribution):[^\n]+)`)
		if match := certNameRe.FindStringSubmatch(string(content)); len(match) > 1 {
			fullCertName = match[1]
		}
	}
	fmt.Println("[INFO] full CODE_SIGN_IDENTITY:", fullCertName)

	// 生成地区目录
	regionDir := filepath.Join(unityProject, "Assets/Editor/Export/iOS/Archive", region)
	os.MkdirAll(regionDir, 0755)

	// 生成build.json内容
	buildJson := map[string]interface{}{
		"options": []string{"iOSNativeSDK"},
		"buildSettings": map[string]interface{}{
			"Unity-iPhone": map[string]interface{}{
				"PRODUCT_BUNDLE_IDENTIFIER":         bundleID,
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]": codeSign,
				"PROVISIONING_PROFILE_SPECIFIER":    provisioningProfile,
				"CODE_SIGN_STYLE":                   "Manual",
				"DEVELOPMENT_TEAM":                  teamID,
				"IPHONEOS_DEPLOYMENT_TARGET":        "13.0",
				"OTHER_CFLAGS":                      "$(inherited)",
				"OTHER_CPLUSPLUSFLAGS":              "$(inherited)",
				"ARCHS":                             "arm64",
			},
			"UnityFramework": map[string]interface{}{
				"ARCHS":                      "arm64",
				"IPHONEOS_DEPLOYMENT_TARGET": "13.0",
			},
		},
		"appendPlist": map[string]interface{}{
			"NSAdvertisingAttributionReportEndpoint": "https://appsflyer-skadnetwork.com/",
			"NSCameraUsageDescription":               "Need to use your camera to take photos to submit feedback to customer service.",
			"NSPhotoLibraryAddUsageDescription":      "Need to access your Media Library to submit feedback to customer service.",
			"NSPhotoLibraryUsageDescription":         "Need to access your Media Library to submit feedback to customer service.",
			"NSUserTrackingUsageDescription":         "Please allow us use your idfa to support you more energy!!!!",
		},
	}
	jsonBytes, _ := json.MarshalIndent(buildJson, "", "    ")
	buildJsonPath := filepath.Join(regionDir, "build.json")
	os.WriteFile(buildJsonPath, jsonBytes, 0644)

	// 生成ExportOptionsPlist目录并写入ExportOptions.plist
	exportPlistDir := filepath.Join(regionDir, "ExportOptionsPlist")
	os.MkdirAll(exportPlistDir, 0755)
	exportPlistPath := filepath.Join(exportPlistDir, "ExportOptions.plist")

	writeMethod := method
	if method == "development" {
		writeMethod = "debugging"
	}

	exportOptionsPlist := `<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>destination</key>
    <string>export</string>
    <key>method</key>
    <string>` + writeMethod + `</string>
    <key>provisioningProfiles</key>
    <dict>
        <key>` + bundleID + `</key>
        <string>` + provisioningProfile + `</string>
    </dict>
    <key>signingCertificate</key>
    <string>` + codeSign + `</string>
    <key>signingStyle</key>
    <string>manual</string>
    <key>stripSwiftSymbols</key>
    <true/>
    <key>teamID</key>
    <string>` + teamID + `</string>
    <key>thinning</key>
    <string>&lt;none&gt;</string>
</dict>
</plist>`
	os.WriteFile(exportPlistPath, []byte(exportOptionsPlist), 0644)

	// 生成一键打ipa脚本 build_ios_project.sh
	buildScript := "#!/bin/bash\n" +
		"\n# --- 脚本使用说明 ---\n" +
		"# projectPath: 你的batchMode中BuildPlayerOptions.locationPathName\n" +
		"# 用法: ./build_ios_project.sh -projectPath <项目路径> [-schemeName <Scheme名称>] -specifier <Provisioning Profile Specifier> -identity <Code Sign Identity>\n" +
		"#\n" +
		"# -projectPath <项目路径>: 你的iOS项目根目录的路径。必需。\n" +
		"# -schemeName <Scheme名称>: (可选) Xcode Scheme的名称。如果未提供，脚本将优先尝试 \"Unity-iPhone\"；若不存在，则自动查找并使用第一个共享 Scheme。\n" +
		"# -specifier <Provisioning Profile Specifier>: Provisioning Profile的标识符。必需。\n" +
		"# -identity <Code Sign Identity>: 代码签名身份。必需。\n" +
		"\n# --- 初始化参数为空 ---\n" +
		"project_path=\"\"\n" +
		"scheme_name=\"Unity-iPhone\" # 默认\n" +
		"provisioning_profile_specifier=\"" + provisioningProfile + "\"\n" +
		"code_sign_identity=\"" + fullCertName + "\"\n" +
		"configType=\"Release\"\n" +
		"\n# --- 解析具名参数 ---\n" +
		"while [[ $# -gt 0 ]]; do\n" +
		"    key=\"$1\"\n" +
		"\n" +
		"    case $key in\n" +
		"        -projectPath)\n" +
		"        project_path=\"$2\"\n" +
		"        shift\n" +
		"        shift\n" +
		"        ;;\n" +
		"        -schemeName)\n" +
		"        scheme_name=\"$2\"\n" +
		"        shift\n" +
		"        shift\n" +
		"        ;;\n" +
		"        -specifier)\n" +
		"        provisioning_profile_specifier=\"$2\"\n" +
		"        shift\n" +
		"        shift\n" +
		"        ;;\n" +
		"        -identity)\n" +
		"        code_sign_identity=\"$2\"\n" +
		"        shift\n" +
		"        shift\n" +
		"        ;;\n" +
		"        *)\n" +
		"        echo \"错误: 未知选项或缺少参数值: $1\"\n" +
		"        echo \"用法: ./build_ios_project.sh -projectPath <项目路径> [-schemeName <Scheme名称>] -specifier <Provisioning Profile Specifier> -identity <Code Sign Identity>\"\n" +
		"        exit 1\n" +
		"        ;;\n" +
		"    esac\n" +
		"done\n" +
		"\n# --- 强制输入校验 (非 Scheme 部分) ---\n" +
		"if [ -z \"$project_path\" ]; then\n" +
		"    echo \"错误: 必须通过 -projectPath 参数指定项目路径。\"\n" +
		"    echo \"用法: ./build_ios_project.sh -projectPath <项目路径> [-schemeName <Scheme名称>] -specifier <Provisioning Profile Specifier> -identity <Code Sign Identity>\"\n" +
		"    exit 1\n" +
		"fi\n" +
		"if [ -z \"$provisioning_profile_specifier\" ]; then\n" +
		"    echo \"错误: 必须通过 -specifier 参数指定Provisioning Profile标识符。\"\n" +
		"    echo \"用法: ./build_ios_project.sh -projectPath <项目路径> [-schemeName <Scheme名称>] -specifier <Provisioning Profile Specifier> -identity <Code Sign Identity>\"\n" +
		"    exit 1\n" +
		"fi\n" +
		"if [ -z \"$code_sign_identity\" ]; then\n" +
		"    echo \"错误: 必须通过 -identity 参数指定代码签名身份。\"\n" +
		"    echo \"用法: ./build_ios_project.sh -projectPath <项目路径> [-schemeName <Scheme名称>] -specifier <Provisioning Profile Specifier> -identity <Code Sign Identity>\"\n" +
		"    exit 1\n" +
		"fi\n" +
		"\n# 获取项目名称，用于归档文件命名\n" +
		"pname=$(basename \"${project_path}\")\n" +
		"\n# 校验项目路径是否存在且包含 .xcodeproj 文件\n" +
		"if [ ! -d \"$project_path\" ]; then\n" +
		"    echo \"错误: 指定的项目路径不存在: $project_path\"\n" +
		"    exit 1\n" +
		"fi\n" +
		"# 确认项目文件类型是 .xcodeproj 还是 .xcworkspace\n" +
		"xcode_target=\"\"\n" +
		"xcode_target_type=\"\"\n" +
		"if [ -f \"${project_path}/Unity-iPhone.xcodeproj/project.pbxproj\" ]; then\n" +
		"    xcode_target=\"${project_path}/Unity-iPhone.xcodeproj\"\n" +
		"    xcode_target_type=\"project\"\n" +
		"elif [ -d \"${project_path}/Unity-iPhone.xcworkspace\" ]; then # 假设工作空间名为Unity-iPhone.xcworkspace\n" +
		"    xcode_target=\"${project_path}/Unity-iPhone.xcworkspace\"\n" +
		"    xcode_target_type=\"workspace\"\n" +
		"else\n" +
		"    echo \"错误: 在项目路径中未找到 Unity-iPhone.xcodeproj 或 Unity-iPhone.xcworkspace。\"\n" +
		"    echo \"请确保 -projectPath 参数指向的是包含 .xcodeproj 或 .xcworkspace 文件的目录。\"\n" +
		"    exit 1\n" +
		"fi\n" +
		"\n# --- Scheme 处理逻辑 ---\n" +
		"if [ -z \"$scheme_name\" ]; then\n" +
		"    echo \"未通过 -schemeName 参数指定 Scheme 名称。\"\n" +
		"    \n" +
		"    # 获取所有共享 Scheme\n" +
		"    available_schemes=$(xcodebuild -\"${xcode_target_type}\" \"${xcode_target}\" -list 2>/dev/null | awk '/Schemes:/ {flag=1; next} /Targets:/ {flag=0} /Build Configurations:/ {flag=0} flag {print $0}')\n" +
		"    available_schemes=$(echo \"$available_schemes\" | sed '/^\\s*$/d') # 清理空行\n" +
		"    \n" +
		"    # 将所有 Scheme 读入数组\n" +
		"    IFS=$'\\n' read -r -d '' -a schemes_array <<< \"$available_schemes\"\n" +
		"\n" +
		"    # 尝试使用 \"Unity-iPhone\" Scheme\n" +
		"    default_unity_scheme=\"Unity-iPhone\"\n" +
		"    if [[ \" ${schemes_array[*]} \" =~ \" ${default_unity_scheme} \" ]]; then\n" +
		"        scheme_name=\"${default_unity_scheme}\"\n" +
		"        echo \"检测到并使用默认 Scheme: ${scheme_name}\"\n" +
		"    else\n" +
		"        echo \"未找到默认 Scheme '${default_unity_scheme}'，尝试查找其他共享 Scheme...\"\n" +
		"        if [ ${#schemes_array[@]} -ge 1 ]; then\n" +
		"            # 找到至少一个 Scheme，使用第一个\n" +
		"            scheme_name=\"${schemes_array[0]}\"\n" +
		"            echo \"自动检测并使用第一个共享 Scheme: ${scheme_name}\"\n" +
		"            if [ ${#schemes_array[@]} -gt 1 ]; then\n" +
		"                echo \"提示: 项目中检测到多个共享 Scheme，但未明确指定。本次构建将使用 '${scheme_name}'。\"\n" +
		"                echo \"所有可用 Scheme 列表:\"\n" +
		"                for s in \"${schemes_array[@]}\"; do\n" +
		"                    echo \"  - $s\"\n" +
		"                done\n" +
		"                echo \"如需指定特定 Scheme，请使用 -schemeName 参数。\"\n" +
		"            fi\n" +
		"        else\n" +
		"            echo \"错误: 未找到任何共享 Scheme。请确保你的 Scheme 已在 Xcode 中设置为共享。\"\n" +
		"            exit 1\n" +
		"        fi\n" +
		"    fi\n" +
		"else\n" +
		"    echo \"已通过 -schemeName 参数指定 Scheme: ${scheme_name}\"\n" +
		"fi\n" +
		"\n" +
		"\n" +
		"echo \"*** 发现iOS工程文件，正在准备导出IPA\"\n" +
		"echo \"项目路径: $project_path\"\n" +
		"\n# 使用传入的 project_path 拼接生成 build_dir\n" +
		"build_dir=\"${project_path}/Build/iOS\"\n" +
		"\n# 如果build_dir存在，则删除重建\n" +
		"if [ -d \"$build_dir\" ]; then\n" +
		"    echo \"正在删除现有构建目录: $build_dir\"\n" +
		"    rm -rf \"$build_dir\"\n" +
		"fi\n" +
		"\n" +
		"echo \"正在创建构建目录: $build_dir\"\n" +
		"mkdir -p \"$build_dir\"\n" +
		"\n" +
		"# --- 正在启动Xcode构建过程 ---\n" +
		"echo \"项目名称 (从路径推断): $pname\"\n" +
		"echo \"Scheme名称: $scheme_name\"\n" +
		"echo \"配置类型: $configType\"\n" +
		"echo \"完整项目路径: $project_path\"\n" +
		"\n" +
		"# --- Xcode Clean ---\n" +
		"echo \"--- 清理 Xcode 构建 ---\"\n" +
		"if [ \"$xcode_target_type\" = \"project\" ]; then\n" +
		"    xcodebuild -quiet clean -configuration \"${configType}\" -scheme \"${scheme_name}\" -project \"${xcode_target}\" -UseModernBuildSystem=YES\n" +
		"else\n" +
		"    xcodebuild -quiet clean -configuration \"${configType}\" -scheme \"${scheme_name}\" -workspace \"${xcode_target}\" -UseModernBuildSystem=YES\n" +
		"fi\n" +
		"\n" +
		"# --- Xcode Archive ---\n" +
		"echo \"--- 归档项目 ---\"\n" +
		"archive_path=\"${build_dir}/${pname}.xcarchive\"\n" +
		"if [ \"$xcode_target_type\" = \"project\" ]; then\n" +
		"    xcodebuild -quiet archive -configuration \"${configType}\" -scheme \"${scheme_name}\" -project \"${xcode_target}\" -archivePath \"${archive_path}\" -UseModernBuildSystem=YES -destination 'generic/platform=iOS' CODE_SIGN_IDENTITY=\"${code_sign_identity}\" PROVISIONING_PROFILE_SPECIFIER=\"${provisioning_profile_specifier}\"\n" +
		"else\n" +
		"    xcodebuild -quiet archive -configuration \"${configType}\" -scheme \"${scheme_name}\" -workspace \"${xcode_target}\" -archivePath \"${archive_path}\" -UseModernBuildSystem=YES -destination 'generic/platform=iOS' CODE_SIGN_IDENTITY=\"${code_sign_identity}\" PROVISIONING_PROFILE_SPECIFIER=\"${provisioning_profile_specifier}\"\n" +
		"fi\n" +
		"\n" +
		"# --- 导出 IPA ---\n" +
		"echo \"--- 导出 IPA 文件 ---\"\n" +
		"export_options_plist=\"${project_path}/ExportOptions.plist\"\n" +
		"if [ ! -f \"${export_options_plist}\" ]; then\n" +
		"    echo \"错误: 未找到导出选项 ExportOptions.plist，请在项目根目录下提供。\"\n" +
		"    exit 1\n" +
		"fi\n" +
		"\n" +
		"xcodebuild -quiet -exportArchive -archivePath \"${archive_path}\" -configuration \"${configType}\" -exportPath \"${build_dir}\" -exportOptionsPlist \"${export_options_plist}\" CODE_SIGN_IDENTITY=\"${code_sign_identity}\" PROVISIONING_PROFILE_SPECIFIER=\"${provisioning_profile_specifier}\"\n" +
		"\n" +
		"echo \"✅ IPA 导出完成，路径: ${build_dir}\"\n"
	os.WriteFile(filepath.Join(regionDir, "build_ios_project.sh"), []byte(buildScript), 0755)
	fmt.Printf("[AutoCreatePackageFolder] unityProject: %s\n", unityProject)
	fmt.Printf("[AutoCreatePackageFolder] region: %s\n", region)
	fmt.Printf("[AutoCreatePackageFolder] mobileprovision: %s\n", mobileprovision)
	fmt.Printf("[AutoCreatePackageFolder] bundleID: %s, teamID: %s, codeSign: %s, provisioningProfile: %s, method: %s\n", bundleID, teamID, codeSign, provisioningProfile, method)
	fmt.Printf("[AutoCreatePackageFolder] 生成目录: %s\n", regionDir)
	fmt.Printf("[AutoCreatePackageFolder] 已生成 build.json: %s\n", buildJsonPath)
	fmt.Printf("[AutoCreatePackageFolder] 已生成 ExportOptionsPlist: %s\n", exportPlistPath)
	fmt.Printf("[AutoCreatePackageFolder] 已生成 build_ios_project.sh: %s\n", filepath.Join(regionDir, "build_ios_project.sh"))
	return nil
}
