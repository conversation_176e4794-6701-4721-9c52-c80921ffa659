package unity

/*
解压 UnityPackage到指定目录
go run extract_unitypackage.go -input <xxx.unitypackage> -output <输出目录可选>
*/

import (
	"archive/tar"
	"compress/gzip"
	"fmt"
	"io"
	"io/ioutil"
	"os"
	"path/filepath"
	"strings"
)

// ExtractUnityPackage 解压 UnityPackage到指定目录
func ExtractUnityPackage(inputFile, outputBase string) error {
	if inputFile == "" {
		return fmt.Errorf("请使用 -input 指定 .unitypackage 文件路径")
	}
	name := strings.TrimSuffix(filepath.Base(inputFile), filepath.Ext(inputFile))
	var outputDir string
	if outputBase != "" {
		outputDir = filepath.Join(outputBase, name)
	} else {
		inputDir := filepath.Dir(inputFile)
		outputDir = filepath.Join(inputDir, name)
	}
	workingDir := ".working"
	if _, err := os.Stat(outputDir); err == nil {
		return fmt.Errorf("Output dir \"%s\" exists. Aborting.", outputDir)
	}
	if _, err := os.Stat(workingDir); err == nil {
		os.RemoveAll(workingDir)
	}
	os.Mkdir(workingDir, 0755)
	f, err := os.Open(inputFile)
	if err != nil {
		return fmt.Errorf("Failed to open input file: %v", err)
	}
	defer f.Close()
	gz, err := gzip.NewReader(f)
	if err != nil {
		return fmt.Errorf("Failed to create gzip reader: %v", err)
	}
	defer gz.Close()
	tarReader := tar.NewReader(gz)

	for {
		hdr, err := tarReader.Next()
		if err == io.EOF {
			break
		}
		if err != nil {
			return fmt.Errorf("Error reading tar: %v", err)
		}
		path := filepath.Join(workingDir, hdr.Name)
		if hdr.FileInfo().IsDir() {
			os.MkdirAll(path, hdr.FileInfo().Mode())
		} else {
			os.MkdirAll(filepath.Dir(path), 0755)
			out, err := os.OpenFile(path, os.O_CREATE|os.O_WRONLY, hdr.FileInfo().Mode())
			if err != nil {
				return fmt.Errorf("Error creating file: %v", err)
			}
			io.Copy(out, tarReader)
			out.Close()
		}
	}

	mapping := make(map[string]string)
	entries, _ := ioutil.ReadDir(workingDir)
	for _, entry := range entries {
		if entry.IsDir() {
			asset := entry.Name()
			rootFile := filepath.Join(workingDir, asset)
			files, _ := ioutil.ReadDir(rootFile)
			realPath := ""
			hasAsset := false
			for _, f := range files {
				if f.Name() == "pathname" {
					b, _ := ioutil.ReadFile(filepath.Join(rootFile, f.Name()))
					lines := strings.Split(string(b), "\n")
					if len(lines) > 0 {
						realPath = strings.TrimSpace(lines[0])
					}
				} else if f.Name() == "asset" {
					hasAsset = true
				}
			}
			if hasAsset && realPath != "" {
				mapping[asset] = realPath
			}
		}
	}

	os.MkdirAll(outputDir, 0755)
	for asset, realPath := range mapping {
		path, filename := filepath.Split(realPath)
		destDir := filepath.Join(outputDir, path)
		destFile := filepath.Join(destDir, filename)
		source := filepath.Join(workingDir, asset, "asset")
		os.MkdirAll(destDir, 0755)
		os.Rename(source, destFile)
		os.Chmod(destFile, 0644)
		fmt.Printf("%s => %s\n", asset, realPath)
	}
	os.RemoveAll(workingDir)
	return nil
}

// 删除 main 函数，已由 ExtractUnityPackage 替代
