package crash

import (
	"bufio"
	"fmt"
	"io"
	"io/fs"
	"onetools/cmd/utility"
	"os"
	"path"
	"path/filepath"
	"regexp"
	"strings"
)

// 查找xcode.app路径
func XCodeAppPath() string {
	xcode_select_cmd := "xcode-select  -p"
	out := utility.ExecuteCommandWithPanic("", xcode_select_cmd, false)
	if out == "" {
		fmt.Printf("%s\n", "未找到xcode.app")
		return ""
	}
	strArr := strings.Split(out, ".app")
	xcode_app_path := fmt.Sprintf("%s%s", strArr[0], ".app")
	// fmt.Println(xcode_app_path)
	return xcode_app_path
}

// 查找CrashSymbolicator.py文件路径
func CrashSymbolicatorPath() string {
	xcodeAppPath := XCodeAppPath()
	symbolicatorName := "CrashSymbolicator.py"
	symbolicatorPath := filepath.Join(xcodeAppPath, "Contents", "SharedFrameworks", "CoreSymbolicationDT.framework", "Versions", "A", "Resources", symbolicatorName)
	if utility.IsExist(symbolicatorPath) {
		return symbolicatorPath
	}

	err := filepath.Walk(xcodeAppPath, func(path string, info fs.FileInfo, err error) error {
		if filepath.Base(info.Name()) == symbolicatorName {
			symbolicatorPath = path
			return io.EOF
		}
		return nil
	})

	if err == io.EOF {
		return symbolicatorPath
	}
	return symbolicatorPath

}

// 查找symbolicatecrash文件路径
func SymbolicateCrashPath() string {
	xcodeAppPath := XCodeAppPath()
	symbolicatorName := "symbolicatecrash"
	symbolicatorPath := filepath.Join(xcodeAppPath, "Contents", "SharedFrameworks", "DVTFoundation.framework", "Versions", "A", "Resources", symbolicatorName)
	if utility.IsExist(symbolicatorPath) {
		return symbolicatorPath
	}

	err := filepath.Walk(xcodeAppPath, func(path string, info fs.FileInfo, err error) error {
		if filepath.Base(info.Name()) == symbolicatorName &&
			strings.Contains(path, "SharedFrameworks") {
			symbolicatorPath = path
			return io.EOF
		}
		return nil
	})

	if err == io.EOF {
		return symbolicatorPath
	}
	return symbolicatorPath

}

// 显示崩溃文件相关UUID，包含目录、.dsym、.app、.crash、.ips
func ShowCrashFileUUID(filePath string) {
	s, _ := os.Stat(filePath)
	if s.IsDir() {
		crashLogSuffix := path.Ext(filePath)
		// .dsym文件
		if strings.EqualFold(crashLogSuffix, ".dsym") {
			showDsymUUID(filePath)
		} else if strings.EqualFold(crashLogSuffix, ".app") {
			//.app文件
			showAppUUID(filePath)
		} else {
			loadCrashFiles(filePath)
		}
	} else {
		if isMachOFile(filePath) {
			symbolCMD := fmt.Sprintf("dwarfdump --uuid '%s'", filePath)
			out := utility.ExecuteCommandWithPanic("", symbolCMD, false)
			if out != "" {
				fmt.Printf("%s", out)
			}
		} else {
			crashLogSuffix := path.Ext(filePath)
			if strings.EqualFold(crashLogSuffix, ".crash") {
				uuidStr := showCrashLogUUID(filePath)
				if len(uuidStr) == 0 {
					//可能是文件格式写错了，再使用.ips检查下
					showIpsLogUUID(filePath)
				}
			} else if strings.EqualFold(crashLogSuffix, ".ips") {
				uuidStr := showIpsLogUUID(filePath)
				if len(uuidStr) == 0 {
					//可能是文件格式写错了，再使用.crash检查下
					showCrashLogUUID(filePath)
				}
			} else {
				fmt.Printf("未能识别的文件类型: %s \n", filePath)
			}
		}
	}
}

func isMachOFile(filename string) bool {
	file, err := os.Open(filename)
	if err != nil {
		fmt.Printf("Error: %v\n", err)
		return false
	}
	defer file.Close()

	buf := make([]byte, 4)
	n, err := file.ReadAt(buf, 0)
	if err != nil || n != 4 {
		return false
	}
	return string(buf) == "\xfe\xed\xfa\xce" || string(buf) == "\xce\xfa\xed\xfe" ||
		string(buf) == "\xfe\xed\xfa\xcf" || string(buf) == "\xcf\xfa\xed\xfe"
}

func loadCrashFiles(dirPath string) {
	s, _ := os.Stat(dirPath)
	if s.IsDir() {
		var crashFileArr []string
		err := filepath.Walk(dirPath, func(path string, info fs.FileInfo, err error) error {
			crashRegex := regexp.MustCompile("(?i)(\\.dsym$|\\.app$|\\.crash$|\\.ips$)")
			if crashRegex.MatchString(path) {
				crashFileArr = append(crashFileArr, path)
			}
			return nil
		})
		if err != nil {
			fmt.Printf("【%s】读取失败,error:%s", dirPath, err)
		}
		for _, filePath := range crashFileArr {
			ShowCrashFileUUID(filePath)
		}
	}
}

func showDsymUUID(filePath string) {
	symbolCMD := fmt.Sprintf("dwarfdump --uuid '%s'", filePath)
	out := utility.ExecuteCommandWithPanic("", symbolCMD, false)
	if out != "" {
		fmt.Printf("%s", out)
	}
}

func showAppUUID(filePath string) {
	logName := filepath.Base(filePath)
	crashLogSuffix := path.Ext(filePath)
	logNameOnly := strings.TrimSuffix(logName, crashLogSuffix)
	appExePath := filepath.Join(filePath, logNameOnly)
	symbolCMD := fmt.Sprintf("dwarfdump --uuid '%s'", appExePath)
	out := utility.ExecuteCommandWithPanic("", symbolCMD, false)
	if out != "" {
		fmt.Printf("%s", out)
	}
}

func showCrashLogUUID(filePath string) string {
	processRegexp, _ := regexp.Compile("Process:(.*?)\\[")
	codeTypeRegexp, _ := regexp.Compile("Code Type:(.*?)\\(")
	var processName = ""
	var codeTypeStr = ""
	var uuidStr = ""
	f, e := os.Open(filePath)
	if e != nil {
		fmt.Printf("【%s】文件读取失败", filePath)
	} else {
		buf := bufio.NewScanner(f)
		for {
			if uuidStr != "" || !buf.Scan() {
				break
			}

			line := buf.Text()
			if processName == "" {
				// 匹配Process
				matchArr := processRegexp.FindStringSubmatch(line)
				if len(matchArr) > 0 {
					// fmt.Println("提取字符串内容：", matchArr[len(matchArr)-1])
					processName = strings.TrimSpace(matchArr[len(matchArr)-1])
				}
			}

			if codeTypeStr == "" {
				// 匹配Code Type
				matchArr := codeTypeRegexp.FindStringSubmatch(line)
				if len(matchArr) > 0 {
					// fmt.Println("提取字符串内容：", matchArr[len(matchArr)-1])
					codeTypeStr = matchArr[len(matchArr)-1]
					codeTypeStr = strings.ToLower(strings.Replace(strings.TrimSpace(codeTypeStr), "-", "", -1))
				}
			}

			if processName != "" && codeTypeStr != "" {
				uuidMatchStr := fmt.Sprintf("%s %s\\s*\\<(.*?)\\>", processName, codeTypeStr)
				uuidRegexp, _ := regexp.Compile(uuidMatchStr)
				matchArr := uuidRegexp.FindStringSubmatch(line)
				if len(matchArr) > 0 {
					uuidStr = strings.ToUpper(matchArr[len(matchArr)-1])
				}
			}
		}
		if uuidStr != "" {
			fmt.Printf("UUID: %s (%s %s) %s\n", uuidStr, processName, codeTypeStr, filePath)
			return uuidStr
		}
	}
	return ""
}

func showIpsLogUUID(filePath string) string {
	processRegexp, _ := regexp.Compile("\"procName\":\"(.*?)\"")
	codeTypeRegexp, _ := regexp.Compile("\"cpuType\":\"(.*?)\"")
	uuidRegexp, _ := regexp.Compile("\"slice_uuid\":\"(.*?)\"")

	var processName = ""
	var codeTypeStr = ""
	var uuidStr = ""
	f, e := os.Open(filePath)
	if e != nil {
		fmt.Printf("【%s】文件读取失败", filePath)
	} else {
		buf := bufio.NewScanner(f)
		for {
			if uuidStr != "" && processName != "" && codeTypeStr != "" {
				break
			}

			if !buf.Scan() {
				break
			}

			line := strings.Replace(buf.Text(), " ", "", -1)
			if processName == "" {
				// 匹配Process
				matchArr := processRegexp.FindStringSubmatch(line)
				if len(matchArr) > 0 {
					processName = strings.TrimSpace(matchArr[len(matchArr)-1])
				}
			}

			if codeTypeStr == "" {
				// 匹配Code Type
				matchArr := codeTypeRegexp.FindStringSubmatch(line)
				if len(matchArr) > 0 {
					codeTypeStr = matchArr[len(matchArr)-1]
					codeTypeStr = strings.ToLower(strings.Replace(codeTypeStr, "-", "", -1))
				}
			}

			matchArr := uuidRegexp.FindStringSubmatch(line)
			if len(matchArr) > 0 {
				uuidStr = strings.ToUpper(matchArr[len(matchArr)-1])
			}
		}
		if uuidStr != "" {
			fmt.Printf("UUID: %s (%s %s) %s\n", uuidStr, processName, codeTypeStr, filePath)
			return uuidStr
		}
	}
	return ""
}
