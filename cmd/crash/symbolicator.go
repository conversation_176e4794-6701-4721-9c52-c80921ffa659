package crash

import (
	"fmt"
	"onetools/cmd/utility"
	"path"
	"path/filepath"
	"regexp"
	"sort"
	"strings"
)

func ParseCrashLog(dsymPath string, crashLogPath string, output string) {
	if !utility.IsExist(dsymPath) {
		panic(fmt.Sprintf("【%s】路径不存在，请确认dsym路径是否正确", dsymPath))
	}

	if !utility.IsExist(crashLogPath) {
		panic(fmt.Sprintf("【%s】路径不存在，请确认crash日志路径是否正确", crashLogPath))
	}
	outputPath := output
	crashLogSuffix := path.Ext(crashLogPath)
	isCrashFile, _ := utility.ContainsKeywordInFile(crashLogPath, "Report Version")

	if outputPath == "" {
		logDir := filepath.Dir(crashLogPath)
		logName := filepath.Base(crashLogPath)
		logNameOnly := strings.TrimSuffix(logName, crashLogSuffix)
		targetLogSuffix := ".crash"
		if !isCrashFile {
			targetLogSuffix = ".ips"
		}
		logOutputName := logNameOnly + "_symbolicated" + targetLogSuffix
		outputPath = filepath.Join(logDir, logOutputName)
	}

	if isCrashFile {
		//.crash文件
		crashSymbolicator := SymbolicateCrashPath()
		xcodeAppPath := XCodeAppPath()
		symbolCMD := fmt.Sprintf("export DEVELOPER_DIR='%s' && '%s' '%s' -d '%s' -o '%s'", xcodeAppPath, crashSymbolicator, crashLogPath, dsymPath, outputPath)
		out := utility.ExecuteCommand("", symbolCMD)
		if out != "" {
			fmt.Println(out)
		}

		fmt.Printf("\n\033[1;32;1m符号化日志生成路径:%s\033[0m\n", outputPath)
	} else {
		// ips文件
		crashSymbolicator := CrashSymbolicatorPath()
		symbolCMD := fmt.Sprintf("python3 '%s' -d '%s' -p '%s' -o '%s'", crashSymbolicator, dsymPath, crashLogPath, outputPath)
		out := utility.ExecuteCommand("", symbolCMD)
		if out != "" {
			fmt.Println(out)
		}
		fmt.Printf("\n\033[1;32;1m符号化日志生成路径:%s\033[0m\n", outputPath)
	}

}

func AtosCrashAddress(dsymPath string, loadAddress string, arch string) {
	if !utility.IsExist(dsymPath) {
		panic(fmt.Sprintf("【%s】路径不存在，请确认dsym路径是否正确", dsymPath))
	}

	addressMatchText := "\\b0x[A-Za-z0-9]+\\b"
	matchRegexp, _ := regexp.Compile(addressMatchText)
	addressArr := matchRegexp.FindAllString(loadAddress, -1)
	// atos -o /DWARF/hmsea  -arch arm64 -l 0x104b60000 0x1057d5050
	if len(addressArr) == 0 {
		panic("loadAddress设置错误，e.g.: -l 0x104b60000 0x1057d5050")
	}

	// 支持一次性传入多个地址，解析出Image load起始地址，其他地址按降序排列
	sort.Sort(sort.Reverse(sort.StringSlice(addressArr)))
	baseAddress := addressArr[len(addressArr)-1]
	crashAddres := addressArr[:len(addressArr)-1]
	addressStr := strings.Join(crashAddres, " ")

	symbolCMD := fmt.Sprintf("atos -o '%s' -arch %s -l %s %s", dsymPath, arch, baseAddress, addressStr)
	out := utility.ExecuteCommand("", symbolCMD)
	if out != "" {
		fmt.Println(out)
	}
}
