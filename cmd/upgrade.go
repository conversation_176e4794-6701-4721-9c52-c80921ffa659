/*
Copyright © 2023 NAME HERE <EMAIL ADDRESS>
*/
package cmd

import (
	"archive/zip"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"onetools/cmd/utility"
	"os"
	"path/filepath"
	"regexp"
	"runtime"
	"strconv"
	"strings"

	"github.com/spf13/cobra"
)

// upgradeCmd represents the upgrade command
var upgradeCmd = &cobra.Command{
	Use:   "upgrade",
	Short: "onetools工具升级命令，将自动升级到最新版本",
	Long: `
自动检测服务器上最新版本，并下载更新到命令所在目录或指定的目录

e.g. 直接更新到命令所在目录: 
	onetools upgrade

e.g. 下载更新命令到指定目录: 
	onetools upgrade -p "/Users/<USER>/Desktop/onetoolsCMD"

e.g. 指定下载的服务器地址: 
	onetools upgrade -u "http://xxxx.com"`,

	Run: func(cmd *cobra.Command, args []string) {
		err, zipUrl := doUpgrade(cmd, args)
		if err != nil {
			fmt.Println("版本升级失败:", err)
			if len(zipUrl) > 0 {
				fmt.Println("可通过以下链接手动下载，完成替换更新：", zipUrl)
			}
		}
	},
}

func init() {
	rootCmd.AddCommand(upgradeCmd)

	upgradeCmd.Flags().StringP("path", "p", "", `【可选】下载命令到指定目录，默认为命令所在当前路径`)

	upgradeCmd.Flags().StringP("url", "u", "", "【可选】检查最新版本的服务器地址，防止后面改动")
}

// upgradeZip 解压并替换指定目录下的文件
func upgradeZip(zipPath string, destDir string) error {
	// 打开 zip 文件
	r, err := zip.OpenReader(zipPath)
	if err != nil {
		return err
	}
	defer r.Close()

	if runtime.GOOS == "windows" {
		// 先判断是否有上次遗留未删除的老版本，有则删除
		old_onetools := filepath.Join(destDir, "onetools_old.exe")
		if utility.IsExist(old_onetools) {
			// fmt.Println("old_version path:", old_onetools)
			err := os.Remove(old_onetools)
			if err != nil {
				// fmt.Println("remove old error :", err)
				return err
			}
		}

		// 再将onetools.exe重命名为onetools_old.exe
		onetoolsExe := filepath.Join(destDir, "onetools.exe")
		if utility.IsExist(onetoolsExe) {
			err := os.Rename(onetoolsExe, old_onetools)
			if err != nil {
				// fmt.Println("rename to old error :", err)
				return err
			}
		}
	}

	// 遍历 zip 文件中的每个文件
	for _, f := range r.File {
		filename := filepath.Base(f.Name)

		// Skip hidden files
		if strings.HasPrefix(filename, ".") {
			continue
		}
		// 构造文件的完整路径
		destFileName := f.Name
		if strings.HasPrefix(destFileName, "onetools/") {
			destFileName = strings.Replace(destFileName, "onetools/", "", 1)
		}

		path := filepath.Join(destDir, destFileName)
		// 如果文件是一个目录，则递归地创建它
		if f.FileInfo().IsDir() {
			err := os.MkdirAll(path, f.Mode())
			if err != nil {
				return err
			}
			continue
		}

		// 打开 zip 文件中的文件
		rc, err := f.Open()
		if err != nil {
			return err
		}
		defer rc.Close()

		// 创建或截断磁盘上的文件
		w, err := os.Create(path)
		if err != nil {
			return err
		}
		defer w.Close()
		// 将文件的内容从 zip 文件中复制到磁盘上的文件
		if _, err := io.Copy(w, rc); err != nil {
			return err
		}

		// 设置文件权限
		if err := os.Chmod(path, f.Mode()); err != nil {
			return err
		}
	}

	if runtime.GOOS == "windows" {
		// 给出删除老版本onetools_old.exe的命令提示
		old_onetools := filepath.Join(destDir, "onetools_old.exe")
		// err := os.Remove(old_onetools)
		// if err != nil {
		// 	return err
		// }
		replaceCMD := fmt.Sprintf("del \"%s\" ", old_onetools)
		fmt.Println("\n", replaceCMD, "\n")
	}
	return nil
}

type UpgradeConfig struct {
	Version     string `json:"version"`
	DownloadUrl string `json:"url"`
}

func doUpgrade(cmd *cobra.Command, args []string) (error, string) {
	targetPath, _ := cmd.Flags().GetString("path")
	if targetPath == "" {
		// 获取当前命令所在目录
		binDir, err := os.Executable()
		if err != nil {
			return err, ""
		}
		targetPath = binDir
		targetPath = filepath.Dir(targetPath)
	}

	hostUrl, _ := cmd.Flags().GetString("url")
	if hostUrl == "" {
		hostUrl = "https://download.wmupd.com:47317/gameResource/Generated/1001/iOS_Onetools_cmdline1"
	}

	// 请求获取版本号信息文件，并当前版本进行比较
	resp, err := http.Get(hostUrl + "/version.json")
	if err != nil {
		return err, ""
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("request Error,http status code: %d", resp.StatusCode), ""
	}

	var config UpgradeConfig
	if err := json.NewDecoder(resp.Body).Decode(&config); err != nil {
		return err, ""
	}

	fullLatestVersion := config.Version
	re := regexp.MustCompile(`V(\d+\.\d+(?:\.\d+)*)`)
	latestVersion := ""
	match := re.FindStringSubmatch(fullLatestVersion)
	if len(match) > 1 {
		latestVersion = match[1]
	} else {
		fmt.Println("更新失败，版本信息文件解析失败")
		return nil, config.DownloadUrl
	}

	// 和当前版本号进行对比
	currentVersion := OneToolsVersion

	result := compareVersions(latestVersion, currentVersion)
	// Print the result.
	switch result {
	case -1:
		// fmt.Printf("%s is less than %s\n", latestVersion, currentVersion)
		fmt.Println("当前已是最新版本!")
		return nil, ""
	case 1:
		// fmt.Printf("%s is greater than %s\n", latestVersion, currentVersion)
		break
	case 0:
		fmt.Println("当前已是最新版本!")
		return nil, ""
	}

	// 下载 zip 文件
	tempDir := utility.GetTempDir()
	zipURL := config.DownloadUrl
	zipPath, err := utility.DownloadFile(zipURL, tempDir)
	if err != nil {
		return err, config.DownloadUrl
	}
	defer os.Remove(zipPath)

	// 解压并替换文件
	fmt.Println("目录路径 :", targetPath)
	err = upgradeZip(zipPath, targetPath)
	if err != nil {
		return err, config.DownloadUrl
	}
	if runtime.GOOS == "windows" {
		fmt.Printf("因Windows平台限制，无法对正在运行的文件删除，可手动执行上面的命令删除老版本\n")
	}
	fmt.Printf("已升级到版本 %s\n", latestVersion)
	return nil, ""
}

func compareVersions(v1, v2 string) int {
	// Split the version strings into parts.
	parts1 := strings.Split(v1, ".")
	parts2 := strings.Split(v2, ".")

	// Determine the maximum length of the two version parts.
	maxLength := len(parts1)
	if len(parts2) > maxLength {
		maxLength = len(parts2)
	}

	// Compare each part.
	for i := 0; i < maxLength; i++ {
		// Get the version part as integers, defaulting to 0 if out of bounds.
		var num1, num2 int
		if i < len(parts1) {
			num1, _ = strconv.Atoi(parts1[i])
		}
		if i < len(parts2) {
			num2, _ = strconv.Atoi(parts2[i])
		}

		// Compare the numeric values.
		if num1 < num2 {
			return -1
		} else if num1 > num2 {
			return 1
		}
	}

	// If all parts are equal, return 0.
	return 0
}
