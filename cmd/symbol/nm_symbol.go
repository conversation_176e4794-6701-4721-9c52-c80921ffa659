package symbol

import (
	"bytes"
	"fmt"
	"io/fs"
	"onetools/cmd/utility"
	"os"
	"os/exec"
)

func NMDirSymbol(targetPath string, symbol string) {
	if !utility.IsExist(targetPath) {
		fmt.Printf("【%s】文件或目录不存在，请检查路径 \n", targetPath)
	}
	s, _ := os.Stat(targetPath)
	if s.IsDir() {
		utility.GetSubContentFromDir(targetPath, func(path string, info fs.FileInfo) {
			if info.IsDir() {
				return
			}
			NMCmdExecute(path, symbol)
		})
	}
}

func NMCmdExecute(path string, symbol string) {
	machoCMD := fmt.Sprintf("file '%s' | grep -i 'Mach-O \\| ar archive'", path)
	var stdout bytes.Buffer
	var cmd exec.Cmd = *exec.Command("/bin/sh", "-c", machoCMD)
	cmd.Stdout = &stdout
	cmd.Run()
	var result string = stdout.String()
	if result == "" {
		return
	}

	var nmCMD string = ""
	if symbol == "" {
		nmCMD = fmt.Sprintf("nm '%s'", path)
	} else {
		nmCMD = fmt.Sprintf("nm -A '%s' -arch arm64 | grep -E '%s'", path, symbol)
	}

	cmd = *exec.Command("/bin/sh", "-c", nmCMD)
	var nmStdout bytes.Buffer
	cmd.Stdout = &nmStdout
	cmd.Run()
	result = nmStdout.String()

	if result != "" {
		fmt.Printf("\n\n【%s】\n %s", path, result)

	}
}
