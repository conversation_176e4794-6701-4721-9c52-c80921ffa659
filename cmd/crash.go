/*
Copyright © 2022 NAME HERE <EMAIL ADDRESS>
*/
package cmd

import (
	"onetools/cmd/crash"
	"onetools/cmd/utility"
	"os"

	"github.com/spf13/cobra"
)

// crashCmd represents the crash command
var crashCmd = &cobra.Command{
	Use:   "crash",
	Short: "针对crashLog进行符号化，也可查看crash相关文件UUID",
	Long: `
符号化崩溃日志，需要先准备好.app.dSYM文件，该文件每次打包都不一样，
将crashlog(.crash或.ips)和.app.dSYM文件最好放到同一级目录下。
使用方法：
e.g. 符号化.crash文件:
	onetools crash -i "xxxx/myapp.crash" -d "xxx/Unity-iPhone.app.dSYM"
	onetools crash -i "xxxx/myapp.crash" -d "xxx/dsymDir/" (dsym自动扫描路径)

e.g. 符号化.ips文件: 
	onetools crash -i "xxxx/mygame.ips" -d "xxx/Unity-iPhone.app.dSYM"
	onetools crash -i "xxxx/mygame.ips" -d "xxx/dsymDir/" (dsym自动扫描路径)

e.g. 符号化crash address(支持传入多个address): 
	onetools crash -l "0x1057d5050 0x104b60000" -d "xxx/Unity-iPhone.app.dSYM"

e.g. 查看crash文件UUID:
	onetools crash -u "xxxx/2022-07-11_13-53-34.crash"
	onetools crash -u "xxxx/2022-07-11_13-53-34.ips"
	onetools crash -u "Unity-iPhone.app.dSYM"
	onetools crash -u "Unity-iPhone.app"
	onetools crash -u "crashFile/" (会自定扫描crashFile目录下的.crash、.ips、.dsym、.app文件)
`,
	Run: func(cmd *cobra.Command, args []string) {

		inputPath, _ := cmd.Flags().GetString("input")
		loadAddress, _ := cmd.Flags().GetString("loadaddress")
		uuidFile, _ := cmd.Flags().GetString("uuid")

		if inputPath == "" && loadAddress == "" && uuidFile == "" {
			panic("crash文件地址和load address不能都为空")
		}

		if inputPath != "" && !utility.IsExist(inputPath) {
			panic("crash文件不存在，请检查路径是否正确")
		}

		dsymPath, _ := cmd.Flags().GetString("dsym")
		outputPath, _ := cmd.Flags().GetString("output")

		if dsymPath == "" {
			pwdPath, _ := os.Getwd()
			dsymPath = pwdPath
		}
		if inputPath != "" {
			crash.ParseCrashLog(dsymPath, inputPath, outputPath)
		}

		if loadAddress != "" {
			arch, _ := cmd.Flags().GetString("arch")
			crash.AtosCrashAddress(dsymPath, loadAddress, arch)
		}

		// 显示uuid
		if uuidFile != "" {
			crash.ShowCrashFileUUID(uuidFile)
		}
	},
}

func init() {
	rootCmd.AddCommand(crashCmd)

	crashCmd.Flags().StringP("input", "i", "", `【可选】crash文件绝对路径，和-l参数二选一`)
	crashCmd.Flags().StringP("dsym", "d", "", "【可选】.dsym符号表文件路径或目录，默认为当前目录")
	crashCmd.Flags().StringP("output", "o", "", "【可选】符号化后Crash日志存储位置，默认生成到input同目录")
	crashCmd.Flags().StringP("loadaddress", "l", "", `【可选】崩溃地址，和-i参数二选一
10  myapp 0x1057d3240 0x104b60000 + 13054528
如上面的0x1057d3240 0x104b60000`)
	crashCmd.Flags().StringP("arch", "a", "arm64", `【可选】默认arm64`)
	crashCmd.Flags().StringP("uuid", "u", "", `【可选】查看指定dsym、crash log的UUID或指定目录自动扫描`)

}
