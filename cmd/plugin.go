package cmd

import (
	"fmt"
	"onetools/cmd/u3d"
	"onetools/cmd/utility"
	"os"
	"path/filepath"

	"github.com/piglig/go-unitypackage"
	"github.com/spf13/cobra"
)

var u3d_version string
var u3d_projectPath string

var importCmd = &cobra.Command{
	Use:   "plugin",
	Short: "安装U3D(SDK导入/iOS打包)插件",
	Long:  `该命令用于安装插件，该插件只能在U3D引擎安装使用`,
	Run: func(cmd *cobra.Command, args []string) {
		u3d_execute(cmd)
	},
}

func init() {
	rootCmd.AddCommand(importCmd)
	importCmd.Flags().StringVarP(&u3d_version, "version", "v", "", `【可选】指定想要安装的插件版本号，默认下载最新版本`)
	importCmd.Flags().StringVarP(&u3d_projectPath, "project", "p", "", `【可选】项目工程绝对路径为空时默认为当前命令执行目录`)
	importCmd.Flags().Bool("list", false, `【可选】查询可安装的版本`)
	importCmd.Flags().BoolP("uninstall", "u", false, `【可选】卸载本地安装的插件`)
	importCmd.Flags().BoolP("install", "i", false, `【可选】安装插件指令`)
	importCmd.Flags().BoolP("snapshot", "s", false, `【可选】指定mvn仓库的环境,默认是release环境`)
	importCmd.Flags().BoolP("export", "e", false, `【可选】指定是否安装导出插件`)
	importCmd.Flags().String("unzip", "", `【可选】指定需要解压unitypackage插件路径`)
}

func u3d_execute(cmd *cobra.Command) {
	// 解压unitypack插件
	zipUnityPackagePath, _ := cmd.Flags().GetString("unzip")
	if zipUnityPackagePath != "" {
		fileNameWithoutExt, _ := utility.GetFileNameWithoutExt(zipUnityPackagePath)
		if fileNameWithoutExt != "" {
			unzipPath := filepath.Join(filepath.Dir(zipUnityPackagePath), fileNameWithoutExt)
			unitypackage.UnPackage(zipUnityPackagePath, unzipPath)
		}
		return
	}

	if u3d_projectPath == "" {
		pwdPath, _ := os.Getwd()
		u3d_projectPath = pwdPath
	}

	if !utility.IsExist(u3d_projectPath) {
		panic(fmt.Sprintf("【%s】路径不存在，请确认project路径是否正确", u3d_projectPath))
	}

	// 获取状态值
	u3d_snapshot, _ := cmd.Flags().GetBool("snapshot")
	u3d_export, _ := cmd.Flags().GetBool("export")

	// 获取版本信息
	checkVersionList, _ := cmd.Flags().GetBool("list")
	if checkVersionList {
		// 展示导入插件的版本信息
		xmlParse := u3d.NewResXmlParse(u3d_getMetadataUrl(false, u3d_snapshot))
		xmlParse.Parse()
		u3d_logLocalVersion(false, u3d_snapshot)
		xmlParse.LogVersionListInfo()
		// 展示工程导出插件的版本信息
		xmlParse = u3d.NewResXmlParse(u3d_getMetadataUrl(true, u3d_snapshot))
		xmlParse.Parse()
		u3d_logLocalVersion(true, u3d_snapshot)
		xmlParse.LogVersionListInfo()
		return
	}

	// 删除本地已安装的插件
	uninstall, _ := cmd.Flags().GetBool("uninstall")
	if uninstall {
		u3d_removeHistoryVersion(u3d_export)
		return
	}

	// 安装插件
	install, _ := cmd.Flags().GetBool("install")
	if install {
		// 安装工程导出插件
		if u3d_export {
			xmlParse := u3d.NewResXmlParse(u3d_getMetadataUrl(true, u3d_snapshot))
			xmlParse.Parse()
			u3d_installPlugin(xmlParse, true, u3d_snapshot)
		} else {
			// 安装SDK导入插件
			xmlParse := u3d.NewResXmlParse(u3d_getMetadataUrl(false, u3d_snapshot))
			xmlParse.Parse()
			u3d_installPlugin(xmlParse, false, u3d_snapshot)
		}
	}
}

// 解析
func u3d_getMetadataUrl(export bool, snapshot bool) string {
	pluginName := "SDKImportTool"
	if export {
		pluginName = "ProjectExportTool"
	}

	environment := "ios-release"
	if snapshot {
		environment = "ios-snapshot"
	}

	return fmt.Sprintf("http://nexus.sys.wanmei.com/repository/%s/com/pwrd/u3d/%s/maven-metadata.xml", environment, pluginName)
}

func u3d_logLocalVersion(export bool, snapshot bool) {
	if export {
		fmt.Println("##########【工程导出插件】##########")
	} else {
		fmt.Println("##########【SDK导入插件】##########")
	}
	fmt.Println("Local Version:")

	localVersion := u3d_getLocalVersion(export, snapshot)
	if localVersion == "" {
		fmt.Printf("当前工程并未安装该插件\n\n")
		return
	}

	fmt.Printf("%s\n\n", localVersion)
}

func u3d_getLocalVersion(export bool, snapshot bool) string {
	pluginName := "SDKImportTools"
	if export {
		pluginName = "Export"
	}

	versionPath := filepath.Join(u3d_projectPath, "Assets", "Editor", pluginName, "Version.txt")

	if !utility.IsExist(versionPath) {
		return ""
	}

	return utility.GetFileContent(versionPath)
}

// 安装指定版本插件
func u3d_installPlugin(xmlParse *u3d.ResXmlParse, export bool, snapshot bool) {
	var fileName string
	if u3d_version == "" {
		fileName = xmlParse.GetNewestFileName()
	} else {
		fileName = xmlParse.GetFileName(u3d_version)
	}

	if !snapshot {
		if fileName == u3d_getLocalVersion(export, snapshot) {
			if export {
				fmt.Println("!!!! 本地安装的【工程导出插件】已经是最新版本 !!!!")
			} else {
				fmt.Println("!!!! 本地安装的【SDK导入插件】已经是最新版本 !!!!")
			}
			return
		}
	}

	// 删除本地已安装的插件版本
	u3d_removeHistoryVersion(export)
	// 安装
	template := u3d.NewBuildGradleTemplate(u3d_projectPath, xmlParse.GetMetadata().GroupId, xmlParse.GetMetadata().ArtifactId, fileName)
	template.GenerateBuildGradleTemplateFile()
	template.ExecuteInstallCommand()
	template.DeleteUselessFiles()
}

// 删除已安装的插件版本
func u3d_removeHistoryVersion(export bool) {
	if export {
		// 删除需要更新的内容
		needDeletedDirPaths := [...]string{
			filepath.Join(u3d_projectPath, "Assets", "Editor", "Export", "iOS", "Archive", "Basic"),
			filepath.Join(u3d_projectPath, "Assets", "Editor", "Export", "iOS", "Script"),
			filepath.Join(u3d_projectPath, "Assets", "Editor", "Export", "iOS", "Shell"),
			filepath.Join(u3d_projectPath, "Assets", "Editor", "Export", "Mac", "Archive", "Basic"),
			filepath.Join(u3d_projectPath, "Assets", "Editor", "Export", "Mac", "Script"),
			filepath.Join(u3d_projectPath, "Assets", "Editor", "Export", "Mac", "Shell"),
			filepath.Join(u3d_projectPath, "Assets", "Editor", "Export", "Version.txt"),
			filepath.Join(u3d_projectPath, "Assets", "Editor", "Export", "version.txt"),
			filepath.Join(u3d_projectPath, "Assets", "Editor", "Export", "iOS", "Version.txt"),
			filepath.Join(u3d_projectPath, "Assets", "Editor", "Export", "iOS", "version.txt"),
			filepath.Join(u3d_projectPath, "Assets", "Packages", "netstandard2.0"),
			filepath.Join(u3d_projectPath, "Assets", "Packages", "Newtonsoft.Json"),
		}

		for _, path := range needDeletedDirPaths {
			if utility.IsExist(path) {
				utility.RemoveDir(path)
			}
		}
	} else {
		pluginPath := filepath.Join(u3d_projectPath, "Assets", "Editor", "SDKImportTools")
		if utility.IsExist(pluginPath) {
			utility.RemoveDir(pluginPath)
		}
	}
}
