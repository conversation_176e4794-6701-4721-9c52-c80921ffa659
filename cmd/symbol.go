/*
Copyright © 2022 NAME HERE <EMAIL ADDRESS>
*/
package cmd

import (
	"onetools/cmd/symbol"
	"onetools/cmd/utility"
	"os"
	"strings"

	"github.com/spf13/cobra"
)

// symbolCmd represents the symbol command
var symbolCmd = &cobra.Command{
	Use:   "symbol",
	Short: "针对ipa文件或目录进行目标符号扫描",
	Long: `
针对ipa文件或目录进行目标符号扫描

e.g. 指定文件夹扫描UIView符号: 
	onetools symbol -p "/Users/<USER>/Desktop/MyProjcet" -t UIView

e.g. 指定文件夹扫描UIView和MyClass符号:
	onetools symbol -p "/Users/<USER>/Desktop/MyProjcet" -t UIView|MyClass

e.g. Lib库扫描UIView符号: 
	onetools symbol -p "/Users/<USER>/Desktop/WMDevice.framework/WMDevice" -t UIView

e.g. ipa包扫描UIView符号(待支持): 
	onetools symbol -p "/Users/<USER>/Desktop/game.ipa" -t UIView
`,
	Run: func(cmd *cobra.Command, args []string) {
		targetPath, _ := cmd.Flags().GetString("path")
		if targetPath == "" {
			pwdPath, _ := os.Getwd()
			targetPath = pwdPath
		}

		if !utility.IsExist(targetPath) {
			panic("文件不存在，请检查路径是否正确")
		}

		symbolStr, _ := cmd.Flags().GetString("target")

		s, _ := os.Stat(targetPath)
		if s.IsDir() {
			symbol.NMDirSymbol(targetPath, symbolStr)
		} else if strings.HasSuffix(targetPath, ".ipa") {

		} else {
			symbol.NMCmdExecute(targetPath, symbolStr)
		}
	},
}

func init() {
	rootCmd.AddCommand(symbolCmd)

	symbolCmd.Flags().StringP("path", "p", "", `【可选】扫描目录或.ipa文件绝对路径，不传默认为当前目录`)

	symbolCmd.Flags().StringP("target", "t", "", "【可选】扫描的目标符号")
}
