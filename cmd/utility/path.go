package utility

import (
	"fmt"
	"os"
	"path/filepath"
	"runtime"
	"strings"
)

///  文件操作模块

func IsExist(path string) bool {
	_, err := os.Stat(path) //os.Stat获取文件信息
	if err != nil {
		if os.IsExist(err) {
			return true
		}
		return false
	}
	return true
}

func IsDir(path string) bool {
	s, err := os.Stat(path)
	if err != nil {
		return false
	}
	return s.IsDir()
}

func IsFile(path string) bool {
	return !IsDir(path)
}

func Mkdir(path string) error {
	err := os.MkdirAll(path, os.ModePerm)
	return err
}

func CreateFile(path string) error {
	f, err := os.Create(path)
	if err != nil {
		return err
	}
	defer f.Close()
	return err
}

func RemoveFile(path string) error {
	err := os.Remove(path)
	return err
}

func RemoveDir(path string) error {
	err := os.RemoveAll(path)
	return err
}

// GetFileSize 获取文件大小
func GetFileSize(path string) int64 {
	fileInfo, err := os.Stat(path)
	if err != nil {
		return 0
	}
	fileSize := fileInfo.Size()
	return fileSize
}

// getTempDir 返回操作系统的临时目录
func GetTempDir() string {
	if runtime.GOOS == "windows" {
		return os.Getenv("TEMP")
	} else {
		return "/tmp"
	}
}

func GetExeDir() string {
	ex, err := os.Executable()
	if err != nil {
		panic(err)
	}
	exPath := filepath.Dir(ex)
	fmt.Println(exPath)
	return exPath
}

func GetFileNameWithoutExt(path string) (string, error) {
	fileName := filepath.Base(path)
	if !strings.Contains(fileName, ".") {
		return "", fmt.Errorf("path[%s] is not file", path)
	}
	return strings.TrimSuffix(fileName, filepath.Ext(path)), nil
}

// EnsureAbsolutePath 检查路径是否为绝对路径，如果是相对路径则转换为绝对路径
// 返回绝对路径和是否为原始绝对路径的标志
func EnsureAbsolutePath(path string) (string, bool) {
	if filepath.IsAbs(path) {
		return path, true
	}

	// 获取当前工作目录
	currentDir, err := os.Getwd()
	if err != nil {
		// 如果获取当前目录失败，返回原路径
		return path, false
	}

	// 将相对路径转换为绝对路径
	absPath := filepath.Join(currentDir, path)
	return absPath, false
}
