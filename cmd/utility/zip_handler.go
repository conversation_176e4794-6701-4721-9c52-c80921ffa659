package utility

import (
	"archive/zip"
	"io"
	"log"
	"os"
	"path"
	"path/filepath"
	"strings"
)

type ZipHandler struct {
	sourcePath string
	destPath   string
}

func NewZipHandler(sourcePath string, destPath string) ZipHandler {
	return ZipHandler{sourcePath: sourcePath, destPath: destPath}
}

func (handler ZipHandler) Zip() (err error) {
	// 创建准备写入的文件
	fw, err := os.Create(handler.destPath)
	defer fw.Close()
	if err != nil {
		return err
	}

	// 通过 fw 来创建 zip.Write
	zw := zip.NewWriter(fw)
	defer func() {
		// 检测一下是否成功关闭
		if err := zw.Close(); err != nil {
			panic(err)
		}
	}()

	// 下面来将文件写入 zw ，因为有可能会有很多个目录及文件，所以递归处理
	return filepath.Walk(handler.sourcePath, func(path string, fi os.FileInfo, errBack error) (err error) {
		if errBack != nil {
			return errBack
		}

		// 通过文件信息，创建 zip 的文件信息
		fh, err := zip.FileInfoHeader(fi)
		fh.Method = zip.Deflate
		if err != nil {
			return
		}

		// 替换文件信息中的文件名
		fh.Name = strings.ReplaceAll(path, filepath.Dir(handler.sourcePath), ".")
		// 这步开始没有加，会发现解压的时候说它不是个目录
		if fi.IsDir() {
			fh.Name += "/"
		}

		// 写入文件信息，并返回一个 Write 结构
		w, err := zw.CreateHeader(fh)
		if err != nil {
			return
		}

		// 检测，如果不是标准文件就只写入头信息，不写入文件数据到 w
		// 如目录，也没有数据需要写
		if !fh.Mode().IsRegular() {
			return nil
		}

		// 打开要压缩的文件
		fr, err := os.Open(path)
		defer fr.Close()
		if err != nil {
			return
		}

		// 将打开的文件 Copy 到 w
		_, errs := io.Copy(w, fr)
		if errs != nil {
			return
		}
		// 输出压缩的内容
		// log.Printf("成功压缩文件： %s, 共写入了 %d 个字符的数据\n", path, n)

		return nil
	})
}

func (handler ZipHandler) Unzip() (string, error) {

	// 打开zip文件
	reader, err := zip.OpenReader(handler.sourcePath)
	if err != nil {
		return "", err
	}

	defer func() {
		err := reader.Close()
		if err != nil {
			log.Fatalf("[unzip]: close reader %s", err.Error())
		}
	}()

	var (
		first string // 记录第一次的解压的名字
		order int    = 0
	)

	for _, file := range reader.File {
		rc, err := file.Open()
		if err != nil {
			return "", err
		}

		filename := filepath.Join(handler.destPath, file.Name)
		//记录第一次的名字
		if order == 0 {
			first = filename
		}
		order += 1
		// log.Println(file.Name)
		if file.FileInfo().IsDir() {
			err = os.MkdirAll(filename, 0777)
			if err != nil {
				return "", err
			}
		} else {
			// 添加该逻辑是，由于文件的父级目录没有创建，导致文件无法被创建，而抛异常
			if path.Dir(filename) != "" && IsDir(path.Dir(filename)) && !IsExist(path.Dir(filename)) {
				os.MkdirAll(path.Dir(filename), 0777)
			}

			w, err := os.Create(filename)
			if err != nil {
				return "", err
			}
			//defer w.Close()
			_, err = io.Copy(w, rc)
			if err != nil {
				return "", err
			}
			iErr := w.Close()
			if iErr != nil {
				log.Fatalf("[unzip]: close io %s", iErr.Error())
			}
			fErr := rc.Close()
			if fErr != nil {
				log.Fatalf("[unzip]: close io %s", fErr.Error())
			}
		}
	}
	return first, nil
}
