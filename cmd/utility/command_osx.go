//go:build !windows
// +build !windows

package utility

import (
	"bytes"
	"fmt"
	"log"
	"os/exec"
)

func ExecuteCommandWithoutRootPath(command string) string {
	return ExecuteCommand("", command)
}

func ExecuteCommand(rootPath string, command string) string {
	return ExecuteCommandWithPanic(rootPath, command, true)
}

func ExecuteCommandWithPanic(rootPath string, command string, canPanic bool) string {
	var stderr bytes.Buffer
	var stdout bytes.Buffer

	var cmd exec.Cmd = *exec.Command("/bin/sh", "-c", command)
	cmd.Dir = rootPath
	cmd.Stderr = &stderr
	cmd.Stdout = &stdout

	// 执行脚本
	err := cmd.Run()
	if err != nil {
		fmt.Println(fmt.Sprint(err) + ": " + stderr.String())
		fmt.Println(stdout.String())
		log.Println(fmt.Sprint(err) + ": " + stderr.String())
		log.Println(stdout.String())
		if canPanic {
			panic(err)
		}
	}

	return stdout.String()
}
