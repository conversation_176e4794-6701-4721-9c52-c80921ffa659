//go:build windows
// +build windows

package utility

import (
	"bytes"
	"fmt"
	"log"
	"os/exec"
	"syscall"
)

func ExecuteCommandWithoutRootPath(command string) string {
	return ExecuteCommand("", command)
}

func ExecuteCommand(rootPath string, command string) string {
	return ExecuteCommandWithPanic(rootPath, command, true)
}

func ExecuteCommandWithPanic(rootPath string, command string, canPanic bool) string {
	var stderr bytes.Buffer
	var stdout bytes.Buffer

	var cmd exec.Cmd = *exec.Command("cmd.exe")
	cmd.SysProcAttr = &syscall.SysProcAttr{CmdLine: fmt.Sprintf("/c %s", command), HideWindow: true}
	if rootPath != "" {
		cmd.Dir = rootPath
	}
	cmd.Stderr = &stderr
	cmd.Stdout = &stdout

	// 执行脚本
	err := cmd.Run()
	if err != nil {
		log.Println(fmt.Sprint(err) + ": " + stderr.String())
		if canPanic {
			panic(err)
		}
	}

	return stdout.String()
}
