package utility

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"log"
	"path/filepath"
	"regexp"
	"strconv"
	"strings"

	"gopkg.in/ini.v1"
)

var UnrealEngineVersion = "4.27.2"
var UEVersionGreaterOrEqual500 = false //UE引擎版本号>=5.0.0
var UEVersionGreaterOrEqual510 = false //UE引擎版本号>=5.1.0
var UEVersionGreaterOrEqual520 = false //UE引擎版本号>=5.2.0
var UEVersionGreaterOrEqual530 = false //UE引擎版本号>=5.3.0
var UEVersionGreaterOrEqual540 = false //UE引擎版本号>=5.4.0

func CheckEnginePath(uePath string) string {
	if !IsExist(uePath) {
		panic(fmt.Sprintf("【%s】路径不存在，请确认该路径是否存在", uePath))
	}
	var realUEPath string
	cleanedUEPath := strings.TrimRight(uePath, "/\\")
	if strings.HasSuffix(cleanedUEPath, "Engine") {
		realUEPath = uePath
	} else {
		realUEPath = filepath.Join(uePath, "Engine")
	}
	return realUEPath
}

// 获取UE引擎版本号
func GetUnrealEngineVersion(uePath string) string {
	ueInstallPath := CheckEnginePath(uePath)
	versionFilePath := filepath.Join(ueInstallPath, "Source", "Runtime", "Launch", "Resources", "Version.h")
	lineBytes, err := ioutil.ReadFile(versionFilePath)
	content := string(lineBytes)
	if err != nil {
		fmt.Printf("Failed to read file: %s\n", err)
		UnrealEngineVersion = ""
		return UnrealEngineVersion
	}
	engineMajorVersion := ""
	engineMinorVersion := ""
	enginePatchVersion := ""

	versionRegex := regexp.MustCompile(`#define ENGINE_MAJOR_VERSION\s+(\d+)`)
	matches := versionRegex.FindStringSubmatch(content)
	if len(matches) >= 2 {
		engineMajorVersion = matches[1]
	} else {
		return UnrealEngineVersion
	}

	versionRegex = regexp.MustCompile(`#define ENGINE_MINOR_VERSION\s+(\d+)`)
	matches = versionRegex.FindStringSubmatch(content)
	if len(matches) >= 2 {
		engineMinorVersion = matches[1]
	} else {
		return UnrealEngineVersion
	}

	versionRegex = regexp.MustCompile(`#define ENGINE_PATCH_VERSION\s+(\d+)`)
	matches = versionRegex.FindStringSubmatch(content)
	if len(matches) >= 2 {
		enginePatchVersion = matches[1]
	} else {
		return UnrealEngineVersion
	}
	// 组成引擎版本字符串
	engineVersionString := fmt.Sprintf("%s.%s.%s", engineMajorVersion, engineMinorVersion, enginePatchVersion)
	UnrealEngineVersion = engineVersionString
	return UnrealEngineVersion
}

// 版本号对比，true:大于等于某个版本 false:小于某个版本
func GreaterOrEqualCompareVersion(version string, targetVersion string) bool {
	versionParts := strings.Split(version, ".")
	targetParts := strings.Split(targetVersion, ".")

	for i := 0; i < len(versionParts) && i < len(targetParts); i++ {
		versionNum, _ := strconv.Atoi(versionParts[i])
		targetNum, _ := strconv.Atoi(targetParts[i])

		if versionNum < targetNum {
			return false
		} else if versionNum > targetNum {
			return true
		}
	}

	// 如果版本号部分相同，但是版本号长度不同，则较长的版本号较新
	return len(versionParts) >= len(targetParts)
}

// IsUEProject 是否为UE项目
func IsUEProject(projectPath string) bool {
	isUEProject, _ := IsUEProjectName(projectPath)
	return isUEProject
}

func IsUEProjectName(projectPath string) (bool, string) {
	files, err := ioutil.ReadDir(projectPath)
	if err != nil {
		panic(err.Error())
	}

	ueProject := false
	projectName := ""
	for _, file := range files {
		// log.Printf("工程根目录下的文件：%s\n", file.Name())
		if strings.HasSuffix(file.Name(), ".uproject") {
			ueProject = true
			projectName = strings.TrimSuffix(file.Name(), ".uproject")
			break
		}
	}
	return ueProject, projectName
}

// 是否是unity项目
func IsUnityProject(projectPath string) bool {
	files, err := ioutil.ReadDir(projectPath)
	if err != nil {
		panic(err.Error())
	}

	unityProject := false

	for _, file := range files {
		// log.Printf("工程根目录下的文件：%s\n", file.Name())
		if strings.HasSuffix(file.Name(), ".csproj") || file.Name() == "Assets" {
			unityProject = true
			break
		}
	}
	return unityProject
}

// 获取UE .uproject工程文件
func GetUEProjectFilePath(projectPath string) string {
	files, err := ioutil.ReadDir(projectPath)
	if err != nil {
		panic(err.Error())
	}
	ueProjectFile := ""

	for _, file := range files {
		if !file.IsDir() && strings.HasSuffix(file.Name(), ".uproject") {
			ueProjectFile = filepath.Join(projectPath, file.Name())
			break
		}
	}
	return ueProjectFile
}

// UEProjectEnableSDKPlugins 获取UE工程目录下，可用的SDK插件
func UEProjectEnableSDKPlugins(projectPath string, sdkDirPath string) []string {

	peojectFilePath := GetUEProjectFilePath(projectPath)
	if peojectFilePath == "" {
		return []string{}
	}

	content, err := ioutil.ReadFile(peojectFilePath)
	if err != nil {
		return []string{}
	}
	projectMap := make(map[string]interface{})
	err = json.Unmarshal(content, &projectMap)
	if err != nil {
		panic(fmt.Sprintf("【%s】解析失败，error:%s", peojectFilePath, err))
	}

	//获取不可用的plugins
	unEnabledPlugins := UEProjectUnenabledPlugins(projectPath)
	// 扫描plugins目录，过滤掉不可用的
	scanPath := filepath.Join(projectPath, "Plugins")
	if len(sdkDirPath) > 0 {
		scanPath = sdkDirPath
	}
	dirs := GetSubDirsNotRecursive(scanPath)
	var defaultModules []string
	for _, subModule := range dirs {
		if !IsContain(unEnabledPlugins, subModule) {
			defaultModules = append(defaultModules, subModule)
		}
	}

	return defaultModules
}

func UEProjectUnenabledPlugins(projectPath string) []string {

	peojectFilePath := GetUEProjectFilePath(projectPath)
	if peojectFilePath == "" {
		return []string{}
	}

	content, err := ioutil.ReadFile(peojectFilePath)
	if err != nil {
		return []string{}
	}
	projectMap := make(map[string]interface{})
	err = json.Unmarshal(content, &projectMap)
	if err != nil {
		panic(fmt.Sprintf("【%s】解析失败，error:%s", peojectFilePath, err))
	}

	//获取不可用的plugins
	var unEnabledPlugins []string
	if projectMap["Plugins"] != nil {
		for _, subPlugin := range projectMap["Plugins"].([]interface{}) {
			plginName := subPlugin.(map[string]interface{})["Name"]
			enable := subPlugin.(map[string]interface{})["Enabled"]
			// fmt.Println("不可用的plugin:", plginName, enable)
			if !enable.(bool) {
				unEnabledPlugins = append(unEnabledPlugins, plginName.(string))
			}
		}
	}
	return unEnabledPlugins
}

// UEProjectPluginsFilter UE插件过滤
func UEProjectPluginsFilter(projectPath string, targetPlugins string, extendPlugins string, platformOS PlatformOS, updateDependency bool) string {
	targetPluginsArray := strings.Split(targetPlugins, ",")
	supportModules := UESupportPlugins()
	supportAllModules := supportModules
	if extendPlugins != "" {
		extendPluginsArray := strings.Split(extendPlugins, ",")
		supportAllModules = append(supportModules, extendPluginsArray...)
		targetPluginsArray = append(targetPluginsArray, extendPluginsArray...)
	}
	// One全球合并后，不同地区依赖的Library，根据isMac，获取Config/[IOS/Mac]/xxGame.ini文件中Region
	if IsContain(targetPluginsArray, "OneEngineSDK") {
		if updateDependency {
			var oneEngineLibray []string
			oneEngineLibrayPath := filepath.Join("OneEngineSDK", "Source", "ThirdParty")
			oneEngineLibray = append(oneEngineLibray, filepath.Join(oneEngineLibrayPath, "OneEngineCoreLibrary"))
			oneEngineLibray = append(oneEngineLibray, filepath.Join(oneEngineLibrayPath, "OneEngineMainlandLibrary"))
			oneEngineLibray = append(oneEngineLibray, filepath.Join(oneEngineLibrayPath, "OneEngineOverseaLibrary"))

			supportAllModules = append(oneEngineLibray, supportModules...)
			targetPluginsArray = append(oneEngineLibray, targetPluginsArray...)
		} else {
			var oneEngineLibray []string
			oneEngineLibrayPath := filepath.Join("OneEngineSDK", "Source", "ThirdParty")
			oneEngineLibray = append(oneEngineLibray, filepath.Join(oneEngineLibrayPath, "OneEngineCoreLibrary"))
			// 读取读取工程配置文件
			if platformOS == PlatformOS_IOS || platformOS == PlatformOS_OSX {
				// iOS和macOS根据获取的配置文件中，Region的值决定目标地区
				targetPlatform := "IOS"
				gameIniFileName := "IOSGame.ini"
				if platformOS == PlatformOS_OSX {
					targetPlatform = "Mac"
					gameIniFileName = "MacGame.ini"
				}
				// 根据服务器端下载配置文件， 判断需要安装的地区库
				var gameIniConfigPath = filepath.Join(projectPath, "Config", targetPlatform, gameIniFileName)
				if IsExist(gameIniConfigPath) {
					var configRegion = UEGetIniConifgValueKey(gameIniConfigPath, "/Script/OneEngineSDK", "Region")
					if strings.EqualFold("1", configRegion) {
						oneEngineLibray = append(oneEngineLibray, filepath.Join(oneEngineLibrayPath, "OneEngineMainlandLibrary"))
					} else if strings.EqualFold("2", configRegion) {
						oneEngineLibray = append(oneEngineLibray, filepath.Join(oneEngineLibrayPath, "OneEngineOverseaLibrary"))
					} else {
					}
				} else {
					log.Printf("【%s】 配置文件不存在，无法确定目标SDK地区，请先通过install -t lib命令下载配置文件\n", gameIniConfigPath)
				}

			} else {
				// 其他平台，默认使用[/Script/OneEngineEditor.OneEngineSettings]中SDKRegion的配置，判断需要安装的地区库
				var gameIniConfigPath = filepath.Join(projectPath, "Config", "DefaultGame.ini")
				if IsExist(gameIniConfigPath) {
					var configRegion = UEGetIniConifgValueKey(gameIniConfigPath, "/Script/OneEngineEditor.OneEngineSettings", "SDKRegion")
					if strings.EqualFold("Mainland", configRegion) {
						oneEngineLibray = append(oneEngineLibray, filepath.Join(oneEngineLibrayPath, "OneEngineMainlandLibrary"))
					} else if strings.EqualFold("Oversea", configRegion) {
						oneEngineLibray = append(oneEngineLibray, filepath.Join(oneEngineLibrayPath, "OneEngineOverseaLibrary"))
					} else {
					}
				}
			}
			supportAllModules = append(oneEngineLibray, supportModules...)
			targetPluginsArray = append(oneEngineLibray, targetPluginsArray...)
		}
	}

	var filterPlugins []string
	for _, subModule := range supportAllModules {
		if IsContain(targetPluginsArray, subModule) {
			filterPlugins = append(filterPlugins, subModule)
		}
	}
	return strings.Join(filterPlugins, ",")
}

// UESupportPlugins SDK支持的UE插件
func UESupportPlugins() []string {
	return []string{"OneSDK", "PWGlobalSDK", "ChatSDK", "WMActivitySDK", "WMGNSDK", "PWCrash", "PWDebug", "PatcherSDK", "NotificationSDK", "PinchFaceSDK"}
}

func UEGetIniConifgValueKey(configFilePath string, configSection string, configKey string) string {
	// 加载配置文件
	config, err := ini.Load(configFilePath)
	if err != nil {
		fmt.Println("无法加载配置文件:", err)
		return ""
	}

	// 获取配置值
	section := config.Section(configSection)
	if section == nil {
		fmt.Println("配置文件读取section失败:", configSection)
		return ""
	}
	value := section.Key(configKey).String()

	return value
}
