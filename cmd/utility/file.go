package utility

import (
	"bytes"
	"fmt"
	"io"
	"os"
	"path/filepath"
)

func FileCopy(src, dst string) (int64, error) {
	sourceFileStat, err := os.Stat(src)
	if err != nil {
		return 0, err
	}

	if !sourceFileStat.Mode().IsRegular() {
		return 0, fmt.Errorf("%s is not a regular file", src)
	}

	source, err := os.Open(src)
	if err != nil {
		return 0, err
	}
	defer source.Close()

	destination, err := os.OpenFile(dst, os.O_CREATE|os.O_TRUNC|os.O_WRONLY, os.ModePerm)
	if err != nil {
		return 0, err
	}
	defer destination.Close()
	nBytes, err := io.Copy(destination, source)
	return nBytes, err
}

// 判断文件是不是压缩文件
func IsCompressedFile(filename string) (bool, error) {
	file, err := os.Open(filename)
	if err != nil {
		return false, err
	}
	defer file.Close()

	buffer := make([]byte, 4)
	_, err = file.Read(buffer)
	if err != nil {
		return false, err
	}

	magicNumbers := map[string][]byte{
		"zip": {0x50, 0x4b, 0x03, 0x04},
		"rar": {0x52, 0x61, 0x72, 0x21},
		// Add more magic numbers for other compressed file formats as needed
	}

	for _, magic := range magicNumbers {
		if bytes.Equal(magic, buffer) {
			// fmt.Println("Detected as", format, "compressed file.")
			return true, nil
		}
	}
	// fmt.Println("Not a compressed file.")
	return false, nil
}

// CopyDir 递归拷贝目录及其内容
func CopyDir(src string, dst string) error {
	// 检查源目录是否存在，不存在，则创建目录
	if !IsExist(dst) {
		err := os.MkdirAll(dst, os.ModePerm)
		if err != nil {
			return err
		}
	}

	// 遍历源目录
	return filepath.Walk(src, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// 计算相对路径
		relPath, err := filepath.Rel(src, path)
		if err != nil {
			return err
		}

		dstPath := filepath.Join(dst, relPath)

		if info.IsDir() {
			// 创建子目录
			return os.MkdirAll(dstPath, info.Mode())
		} else {
			// 拷贝文件
			_, error := FileCopy(path, dstPath)
			return error
		}
	})
}
