package utility

import (
	"bytes"
	"encoding/binary"
	"io"
	"os"

	"howett.net/plist"
)

// ParsePlist 可直接用于解析 Info.plist
func ParsePlist(filePath string) (map[string]interface{}, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return nil, err
	}
	d := plist.NewDecoder(file)
	var output map[string]interface{}
	err = d.Decode(&output)
	if err != nil {
		return nil, err
	}
	return output, nil
}

func ParsePlistBytes(buff []byte) (map[string]interface{}, error) {
	reader := bytes.NewReader([]byte(buff))
	d := plist.NewDecoder(reader)
	var output map[string]interface{}
	err := d.Decode(&output)
	if err != nil {
		return nil, err
	}
	return output, nil
}

func ParsePlistWithReader(reader io.ReadSeeker) (map[string]interface{}, error) {
	d := plist.NewDecoder(reader)
	var output map[string]interface{}
	err := d.Decode(&output)
	if err != nil {
		return nil, err
	}
	return output, nil
}

func ParseEmbeddedMobileProvisionFile(filePath string) map[string]interface{} {
	file, _ := os.Open(filePath)
	defer func(file *os.File) {
		_ = file.Close()
	}(file)
	all, _ := io.ReadAll(file)
	startIndex := bytes.Index(all, []byte("<?xml"))
	_, _ = file.Seek(int64(startIndex-2), 0)
	var lengthBuff [2]byte
	_, _ = file.Read(lengthBuff[0:])
	xmlLen := binary.BigEndian.Uint16(lengthBuff[0:])
	contents, _ := ParsePlistWithReader(io.NewSectionReader(file, int64(startIndex), int64(xmlLen)))
	return contents
}

// MergePlistFiles 合并两个plist文件的内容
// 将sourcePlistPath文件中的key合并到targetPlistPath文件中
// 如果targetPlistPath中已经存在相同的key，则会被sourcePlistPath中的值替换
// 只合并最外层的key
func MergePlistFiles(targetPlistPath, sourcePlistPath string) (map[string]interface{}, error) {
	// 解析目标plist文件
	targetMap, err := ParsePlist(targetPlistPath)
	if err != nil {
		return nil, err
	}

	// 解析源plist文件
	sourceMap, err := ParsePlist(sourcePlistPath)
	if err != nil {
		return nil, err
	}

	// 合并最外层key
	for key, value := range sourceMap {
		targetMap[key] = value
	}

	return targetMap, nil
}
