package ubt

import (
	"onetools/cmd/utility"
	"path/filepath"
)

/*********************************************************************/
/*              修改IOSToolChain.cs,添加支持Swift编译链接                */
/*********************************************************************/
func ModifyIOSToolChainSource(ueRoot string, isAddCode bool) bool {
	ubtFilePath := filepath.Join(ueRoot, "Source", "Programs", "UnrealBuildTool", "Platform", "IOS", "IOSToolChain.cs")

	originalKeyword := "-rpath \\\"/usr/lib/swift\\\""
	insetCodeBeginFlag := "//--------- Modified Swift Link by WMBuild Start"
	insetCodeEndFlag := "//--------- Modified Swift Link by WMBuild END"

	// 需要插入的代码
	insertTargetCodeArray := []string{}
	matchTargetText := ""
	if utility.UEVersionGreaterOrEqual540 {
		insertTargetCodeArray = []string{
			(insetCodeBeginFlag),
			("\t\t\t// enable swift support"),
			("\t\t\tArguments.Add(\"-rpath \\\"/usr/lib/swift\\\"\");"),
			("\t\t\tArguments.Add(\"-rpath \\\"@executable_path/Frameworks\\\"\");"),
			("\t\t\t// /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS.sdk/usr/lib/swift/"),
			("\t\t\tString swiftLibPath = String.Format(\"-L {0}/usr/lib/swift\",SDKPath);"),
			("\t\t\tArguments.Add(swiftLibPath);"),
			("\t\t\tLog.TraceInformation(\"Add swift lib path: {0}\", swiftLibPath);"),
			("\t\t\t// /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphoneos"),
			("\t\t\tswiftLibPath = String.Format(\"-L {0}/../lib/swift/{1}\",Settings.ToolchainDir, LinkEnvironment.Architecture == UnrealArch.IOSSimulator? \"iphonesimulator\" : \"iphoneos\");"),
			("\t\t\tArguments.Add(swiftLibPath);"),
			("\t\t\tLog.TraceInformation(\"Add swift lib path : {0}\", swiftLibPath);"),
			("\t\t\t// /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift-5.0/iphoneos"),
			("\t\t\tswiftLibPath = String.Format(\"-L {0}/../lib/swift-5.0/{1}\",Settings.ToolchainDir,  LinkEnvironment.Architecture == UnrealArch.IOSSimulator? \"iphonesimulator\" : \"iphoneos\");"),
			("\t\t\tArguments.Add(swiftLibPath);"),
			("\t\t\tif (Settings.SDKVersionFloat >= 14.0f)"),
			("\t\t\t{"),
			("\t\t\t\tArguments.Add(String.Format(\"-lswiftCompatibility51\"));"),
			("\t\t\t}"),
			("\t\t\t" + insetCodeEndFlag)}

		//插入目标位置下一段关键代码
		matchTargetText = "Arguments.Add\\(\"-rpath @executable_path/Frameworks\"\\);"
	} else if utility.UEVersionGreaterOrEqual510 {
		insertTargetCodeArray = []string{
			(insetCodeBeginFlag),
			("\t\t\t// enable swift support"),
			("\t\t\tArguments.Add(\"-rpath \\\"/usr/lib/swift\\\"\");"),
			("\t\t\tArguments.Add(\"-rpath \\\"@executable_path/Frameworks\\\"\");"),
			("\t\t\t// /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS.sdk/usr/lib/swift/"),
			("\t\t\tString swiftLibPath = String.Format(\"-L {0}Platforms/{1}.platform/Developer/SDKs/{1}{2}.sdk/usr/lib/swift\",Settings.Value.XcodeDeveloperDir, bIsDevice? Settings.Value.DevicePlatformName : Settings.Value.SimulatorPlatformName, Settings.Value.IOSSDKVersion);"),
			("\t\t\tArguments.Add(swiftLibPath);"),
			("\t\t\tLog.TraceInformation(\"Add swift lib path: {0}\", swiftLibPath);"),
			("\t\t\t// /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphoneos"),
			("\t\t\tswiftLibPath = String.Format(\"-L {0}Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/{1}\",Settings.Value.XcodeDeveloperDir, bIsDevice? Settings.Value.DevicePlatformName.ToLower() : Settings.Value.SimulatorPlatformName.ToLower());"),
			("\t\t\tArguments.Add(swiftLibPath);"),
			("\t\t\tLog.TraceInformation(\"Add swift lib path : {0}\", swiftLibPath);"),
			("\t\t\t// /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift-5.0/iphoneos"),
			("\t\t\tswiftLibPath = String.Format(\"-L {0}Toolchains/XcodeDefault.xctoolchain/usr/lib/swift-5.0/{1}\",Settings.Value.XcodeDeveloperDir, bIsDevice? Settings.Value.DevicePlatformName.ToLower() : Settings.Value.SimulatorPlatformName.ToLower());"),
			("\t\t\tArguments.Add(swiftLibPath);"),
			("\t\t\tif (Settings.Value.IOSSDKVersionFloat >= 14.0f)"),
			("\t\t\t{"),
			("\t\t\t\tArguments.Add(String.Format(\"-lswiftCompatibility51\"));"),
			("\t\t\t}"),
			("\t\t\t" + insetCodeEndFlag)}

		//插入目标位置下一段关键代码
		matchTargetText = "Arguments.Add\\(\"-rpath @executable_path/Frameworks\"\\);"
	} else {
		insertTargetCodeArray = []string{
			(insetCodeBeginFlag),
			("\t\t\t// enable swift support"),
			("\t\t\tResult += \" -rpath \\\"/usr/lib/swift\\\"\";"),
			("\t\t\tResult += \" -rpath \\\"@executable_path/Frameworks\\\"\";"),
			("\t\t\t// /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS.sdk/usr/lib/swift/"),
			("\t\t\tString swiftLibPath = String.Format(\" -L {0}Platforms/{1}.platform/Developer/SDKs/{1}{2}.sdk/usr/lib/swift\",Settings.Value.XcodeDeveloperDir, bIsDevice? Settings.Value.DevicePlatformName : Settings.Value.SimulatorPlatformName, Settings.Value.IOSSDKVersion);"),
			("\t\t\tResult += swiftLibPath;"),
			("\t\t\tLog.TraceInformation(\"Add swift lib path: {0}\", swiftLibPath);"),
			("\t\t\t// /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphoneos"),
			("\t\t\tswiftLibPath = String.Format(\" -L {0}Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/{1}\",Settings.Value.XcodeDeveloperDir, bIsDevice? Settings.Value.DevicePlatformName.ToLower() : Settings.Value.SimulatorPlatformName.ToLower());"),
			("\t\t\tResult += swiftLibPath;"),
			("\t\t\tLog.TraceInformation(\"Add swift lib path : {0}\", swiftLibPath);"),
			("\t\t\t// /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift-5.0/iphoneos"),
			("\t\t\tswiftLibPath = String.Format(\" -L {0}Toolchains/XcodeDefault.xctoolchain/usr/lib/swift-5.0/{1}\",Settings.Value.XcodeDeveloperDir, bIsDevice? Settings.Value.DevicePlatformName.ToLower() : Settings.Value.SimulatorPlatformName.ToLower());"),
			("\t\t\tResult += swiftLibPath;"),
			("\t\t\tif (Settings.Value.IOSSDKVersionFloat >= 14.0f)"),
			("\t\t\t{"),
			("\t\t\t\tResult += String.Format(\" -lswiftCompatibility51\");"),
			("\t\t\t}"),
			("\t\t\t" + insetCodeEndFlag)}

		//插入目标位置下一段关键代码
		matchTargetText = "Result \\+= \" -rpath @executable_path/Frameworks\";"
	}

	placeholderText := "\t\t\t"
	status, message := InsertUBTSource(ubtFilePath, originalKeyword, insetCodeBeginFlag, insetCodeEndFlag, insertTargetCodeArray, matchTargetText, placeholderText, isAddCode, "")
	return CheckFixUBTResult("IOSToolChain:swift", status, message)
}

/*********************************************************************/
/*              修改编译参数,修改Xcode14.3以上版本编译报错                */
/*********************************************************************/
func ModifyIOSToolChainCompileArguments(ueRoot string, isAddCode bool) bool {
	ubtFilePath := filepath.Join(ueRoot, "Source", "Programs", "UnrealBuildTool", "Platform", "IOS", "IOSToolChain.cs")

	originalKeyword := "-Wno-error=bitwise-instead-of-logical"
	insetCodeBeginFlag := "//--------- Modified ClangVersion14 by WMBuild Start"
	insetCodeEndFlag := "//--------- Modified ClangVersion14 by WMBuild END"

	// 需要插入的代码
	// 需要插入的代码
	insertTargetCodeArray := []string{}
	if utility.UEVersionGreaterOrEqual510 {
		//大于5.1.0的不需要修改，ClangToolChain.cs已支持
		return false
	} else {
		insertTargetCodeArray = []string{
			(insetCodeBeginFlag),
			("\t\t\tif (GetClangVersion().Major >= 14)"),
			("\t\t\t{"),
			("\t\t\t\tResult += \" -Wno-error=bitwise-instead-of-logical\";"),
			("\t\t\t}"),
			("\t\t\tif (GetClangVersion().Major >= 15)"),
			("\t\t\t{"),
			("\t\t\t\tResult += \" -Wno-deprecated-builtins\";"),
			("\t\t\t\tResult += \" -Wno-single-bit-bitfield-constant-conversion\";"),
			("\t\t\t}"),
			("\t\t\t" + insetCodeEndFlag)}
	}

	//插入目标位置下一段关键代码
	matchTargetText := "// Add additional frameworks so that their headers can be found"
	placeholderText := "\t\t\t"
	status, message := InsertUBTSource(ubtFilePath, originalKeyword, insetCodeBeginFlag, insetCodeEndFlag, insertTargetCodeArray, matchTargetText, placeholderText, isAddCode, "")
	return CheckFixUBTResult("IOSToolChain:CompileArg", status, message)
}
