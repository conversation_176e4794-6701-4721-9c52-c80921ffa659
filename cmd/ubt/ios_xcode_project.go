package ubt

import (
	"onetools/cmd/utility"
	"path/filepath"
)

/*********************************************************************/
/*      修改XcodeProject.cs，对应XcodeProject,添加iOS12以下Swift支持     */
/*********************************************************************/
func ModifyXcodeProjectSource(ueRoot string, isAddCode bool) bool {
	ubtFilePath := filepath.Join(ueRoot, "Source", "Programs", "UnrealBuildTool", "ProjectFiles", "Xcode", "XcodeProject.cs")

	originalKeyword := "ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES"
	insetCodeBeginFlag := "//--------- Modified XcodeProject by WMBuild Start"
	insetCodeEndFlag := "//--------- Modified XcodeProject by WMBuild END"

	// 需要插入的代码
	insertTargetCodeArray := []string{}
	matchTargetText := ""
	placeholderText := ""
	if utility.UEVersionGreaterOrEqual530 {
		return false
	} else if utility.UEVersionGreaterOrEqual510 {
		insertTargetCodeArray = []string{
			(insetCodeBeginFlag),
			("\t\t\tXcconfig.AppendLine(\"ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES\");"),
			("\t\t\t" + insetCodeEndFlag)}

		//插入目标位置下一段关键代码
		matchTargetText = "if \\(UnrealData.bWriteCodeSigningSettings\\)"
		placeholderText = "\t\t\t"
	} else {
		insertTargetCodeArray = []string{
			(insetCodeBeginFlag),
			("\t\t\t\tContent.Append(\"\\t\\t\\t\\tALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;\" + ProjectFileGenerator.NewLine);"),
			("\t\t\t\t" + insetCodeEndFlag)}

		//插入目标位置下一段关键代码
		matchTargetText = "Content.Append.*SUPPORTED_PLATFORMS.*\\);\\s*if \\(bAutomaticSigning\\)"
		placeholderText = "\t\t\t\t"
	}

	status, message := InsertUBTSource(ubtFilePath, originalKeyword, insetCodeBeginFlag, insetCodeEndFlag, insertTargetCodeArray, matchTargetText, placeholderText, isAddCode, "")
	return CheckFixUBTResult("XcodeProject:swift", status, message)
}

/*********************************************************************/
/*      修改XcodeProject.File.cs，对应UE5.3及以上添加.plist资源          */
/*********************************************************************/
func ModifyResourceExtensions(ueRoot string, isAddCode bool) bool {
	ubtFilePath := filepath.Join(ueRoot, "Source", "Programs", "UnrealBuildTool", "ProjectFiles", "Xcode", "XcodeProject.File.cs")

	originalKeyword := "\t\".plist\",\n"
	insetCodeBeginFlag := "//--------- Modified ResourceExtensions by WMBuild Start"
	insetCodeEndFlag := "//--------- Modified ResourceExtensions by WMBuild END"

	// 需要插入的代码
	insertTargetCodeArray := []string{}
	matchTargetText := ""
	placeholderText := ""
	if utility.UEVersionGreaterOrEqual530 {
		insertTargetCodeArray = []string{
			(insetCodeBeginFlag),
			("\t\t\t\".plist\","),
			("\t\t\t\".config\","),
			("\t\t\t" + insetCodeEndFlag)}

		//插入目标位置下一段关键代码
		matchTargetText = "// no extensions will be folders, like \"cookeddata\" that we want to copy as a resource"
		placeholderText = "\t\t\t"
		status, message := InsertUBTSource(ubtFilePath, originalKeyword, insetCodeBeginFlag, insetCodeEndFlag, insertTargetCodeArray, matchTargetText, placeholderText, isAddCode, "")
		return CheckFixUBTResult("XcodeProject:add\".Plist\"", status, message)

	} else {
		return false
	}
}
