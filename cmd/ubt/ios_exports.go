package ubt

import (
	"onetools/cmd/utility"
	"path/filepath"
)

/*********************************************************************/
/*             修改IOSExports.cs，对应Entitlements                     */
/*********************************************************************/
func ModifyIOSExportsSource(ueRoot string, isAddCode bool) bool {
	ubtFilePath := filepath.Join(ueRoot, "Source", "Programs", "UnrealBuildTool", "Platform", "IOS", "IOSExports.cs")
	originalKeyword := "<key>com.apple.developer.associated-domains</key>"
	insetCodeBeginFlag := "//--------- Modified Entitlement by WMBuild Start"
	insetCodeEndFlag := "//--------- Modified Entitlement by WMBuild END"

	// 需要插入的代码
	insertTargetCodeArray := []string{
		// AssociatedDomains
		insetCodeBeginFlag,
		("\t\t\t\tbool bEnableAssociatedDomains = false;"),
		("\t\t\t\tPlatformGameConfig.GetBool(\"/Script/IOSConfig.IOSConfigSettings\", \"bEnableAssociatedDomains\", out bEnableAssociatedDomains);"),
		("\t\t\t\tif (bEnableAssociatedDomains)"),
		("\t\t\t\t{"),
		("\t\t\t\t\tConsole.WriteLine(\"WMSDK adding AssociatedDomains to entitlements...\");"),
		("\t\t\t\t\tList<string> AssociatedDomains;"),
		("\t\t\t\t\tPlatformGameConfig.GetArray(\"/Script/IOSConfig.IOSConfigSettings\", \"AssociatedDomains\", out AssociatedDomains);"),
		("\t\t\t\t\tif (AssociatedDomains.Count > 0)"),
		("\t\t\t\t\t{"),
		("\t\t\t\t\t\tText.AppendLine(\"\\t<key>com.apple.developer.associated-domains</key>\");"),
		("\t\t\t\t\t\tText.AppendLine(\"\\t<array>\");"),
		("\t\t\t\t\t\tforeach (string i in AssociatedDomains)"),
		("\t\t\t\t\t\t{"),
		("\t\t\t\t\t\t\tText.AppendLine(string.Format(\"\\t\\t<string>{0}</string>\", i));"),
		("\t\t\t\t\t\t}"),
		("\t\t\t\t\t\tText.AppendLine(\"\\t</array>\");"),
		("\t\t\t\t\t}"),
		("\t\t\t\t}"),
		//SignInwithApple
		("\t\t\t\tbool bEnableSignInwithApple = false;"),
		("\t\t\t\tPlatformGameConfig.GetBool(\"/Script/IOSConfig.IOSConfigSettings\", \"bEnableSignInwithApple\", out bEnableSignInwithApple);"),
		("\t\t\t\tif (bEnableSignInwithApple)"),
		("\t\t\t\t{"),
		("\t\t\t\t\tConsole.WriteLine(\"WMSDK adding SignInwithApple to entitlements...\");"),
		("\t\t\t\t\tText.AppendLine(\"\\t<key>com.apple.developer.applesignin</key>\");"),
		("\t\t\t\t\tText.AppendLine(\"\\t<array>\");"),
		("\t\t\t\t\tText.AppendLine(\"\\t\\t<string>Default</string>\");"),
		("\t\t\t\t\tText.AppendLine(\"\\t</array>\");"),
		("\t\t\t\t}"),
		//PushNotifications
		("\t\t\t\tbool bEnablePushNotifications = false;"),
		("\t\t\t\tPlatformGameConfig.GetBool(\"/Script/IOSConfig.IOSConfigSettings\", \"bEnablePushNotifications\", out bEnablePushNotifications);"),
		("\t\t\t\tif (bEnablePushNotifications)"),
		("\t\t\t\t{"),
		("\t\t\t\t\tConsole.WriteLine(\"WMSDK adding Push Notifications to entitlements...\");"),
		("\t\t\t\t\tText.AppendLine(\"\\t<key>aps-environment</key>\");"),
		("\t\t\t\t\tText.AppendLine(string.Format(\"\\t<string>{0}</string>\", bForDistribution ? \"production\" : \"development\"));"),
		("\t\t\t\t}"),
		("\t\t\t\t" + insetCodeEndFlag)}

	//插入目标位置下一段关键代码
	matchTargetText := "Text.AppendLine\\(\"</dict>\"\\);(\\s)*Text.AppendLine\\(\"</plist>\"\\);"
	placeholderText := "\t\t\t\t"
	status, message := InsertUBTSource(ubtFilePath, originalKeyword, insetCodeBeginFlag, insetCodeEndFlag, insertTargetCodeArray, matchTargetText, placeholderText, isAddCode, "")
	addSourceCode := CheckFixUBTResult("IOSExports:capabilities", status, message)

	// UE5.3, 添加using System.Collections.Generic;头文件
	addHeaderStatus := false
	if utility.UEVersionGreaterOrEqual530 {
		addHeaderStatus = AddUsingHeaderIOSExports(ubtFilePath, isAddCode)
	}
	return addSourceCode || addHeaderStatus
}

func AddUsingHeaderIOSExports(ubtFilePath string, isAddCode bool) bool {
	originalKeyword := "System.Collections.Generic;"
	insetCodeBeginFlag := "//--------- Modified Import Header by WMBuild Start"
	insetCodeEndFlag := "//--------- Modified Import Header by WMBuild END"

	// 需要插入的代码
	matchTargetText := ""
	placeholderText := ""
	insertTargetCodeArray := []string{
		(insetCodeBeginFlag),
		("using System.Collections.Generic;"),
		(insetCodeEndFlag)}

	//插入目标位置下一段关键代码
	matchTargetText = "namespace UnrealBuildTool"
	placeholderText = ""

	status, message := InsertUBTSource(ubtFilePath, originalKeyword, insetCodeBeginFlag, insetCodeEndFlag, insertTargetCodeArray, matchTargetText, placeholderText, isAddCode, "")
	return CheckFixUBTResult("IOSExports:addHeader", status, message)
}
