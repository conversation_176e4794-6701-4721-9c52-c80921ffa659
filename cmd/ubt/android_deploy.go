package ubt

import (
	"path/filepath"
)

/***********************************************************************************/
/*                          修改Android UEDeployAndroid.cs                          */
/***********************************************************************************/
func ModifyAndroidDeploySource(ueRoot string, isAddCode bool) bool {
	ubtFilePath := filepath.Join(ueRoot, "Source", "Programs", "UnrealBuildTool", "Platform", "Android", "UEDeployAndroid.cs")

	originalKeyword := "UPL.ProcessPluginNode(NDKArch, \"gameApplicationSuperClass\", \"\");"
	insetCodeBeginFlag := "//--------- Modified DeployAndriod by WMBuild Start"
	insetCodeEndFlag := "//--------- Modified DeployAndriod by WMBuild END"

	// 需要插入的代码
	insertTargetCodeArray := []string{
		(insetCodeBeginFlag),
		("\t\t\tstring SuperApplicationDefault = UPL.ProcessPluginNode(NDKArch, \"gameApplicationSuperClass\", \"\");"),
		("\t\t\tif (String.IsNullOrEmpty(SuperApplicationDefault))"),
		("\t\t\t{"),
		("\t\t\t\tSuperApplicationDefault = \"Application\";"),
		("\t\t\t}"),
		("\t\t\tReplacements.Add(\"$${gameApplicationSuperClass}$$\", SuperApplicationDefault);"),
		("\t\t\t" + insetCodeEndFlag)}

	//插入目标位置下一段关键代码
	matchTargetText := "string\\[\\] TemplateSrc \\= File.ReadAllLines\\(SourceFilename\\);(\\s)*string\\[\\] TemplateDest \\= File.Exists\\(DestFilename\\)"
	placeholderText := "\t\t\t"
	matchBeginText := "private void UpdateGameApplication\\(string UE4Arch, string NDKArch, string EngineDir, string UE4BuildPath\\)"
	status, message := InsertUBTSource(ubtFilePath, originalKeyword, insetCodeBeginFlag, insetCodeEndFlag, insertTargetCodeArray, matchTargetText, placeholderText, isAddCode, matchBeginText)
	return CheckFixUBTResult("DeployAndroid", status, message)
}
