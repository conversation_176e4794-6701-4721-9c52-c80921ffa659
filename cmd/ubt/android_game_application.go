package ubt

import (
	"path/filepath"
)

/***********************************************************************************/
/*                   修改Android GameApplication.java.template                      */
/***********************************************************************************/
func ReplaceGameApplicationSource(ueRoot string, isAddCode bool) bool {
	ubtFilePath := filepath.Join(ueRoot, "Build", "Android", "Java", "src", "com", "epicgames", "ue4", "GameApplication.java.template")

	originalKeyword := "extends $${gameApplicationSuperClass}$$"
	insetCodeBeginFlag := "//--------- Modified GameApplication by W<PERSON><PERSON> Start"
	insetCodeEndFlag := "//--------- Modified GameApplication by WMBuild END"

	// 需要插入的代码
	insertTargetCodeArray := []string{
		(insetCodeBeginFlag),
		("public class GameApplication extends $${gameApplicationSuperClass}$$ implements LifecycleObserver {"),
		(insetCodeEndFlag)}

	//插入目标位置下一段关键代码
	matchTargetText := "public class GameApplication extends Application implements LifecycleObserver {"
	placeholderText := ""
	status, message := ReplaceUBTSource(ubtFilePath, originalKeyword, insetCodeBeginFlag, insetCodeEndFlag, insertTargetCodeArray, matchTargetText, placeholderText, isAddCode, "")
	return CheckFixUBTResult("gameApplication", status, message)
}
