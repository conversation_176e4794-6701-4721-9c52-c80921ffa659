package ubt

import (
	"onetools/cmd/utility"
	"path/filepath"
)

/***********************************************************************************/
/*                 				增加-ObjC编译参数	  	                             */
/***********************************************************************************/
func ModifyCompileArgumentsObjC(ueRoot string, isAddCode bool) bool {
	ubtFilePath := filepath.Join(ueRoot, "Source", "Programs", "UnrealBuildTool", "Platform", "Mac", "MacToolChain.cs")

	originalKeyword := "-ObjC"
	insetCodeBeginFlag := "//--------- Modified macOS ObjC by WMBuild Start"
	insetCodeEndFlag := "//--------- Modified macOS ObjC by WMBuild END"

	// 需要插入的代码
	insertTargetCodeArray := []string{}
	if utility.UEVersionGreaterOrEqual540 {
		insertTargetCodeArray = []string{
			(insetCodeBeginFlag),
			("\t\t\tArguments.Add(\"-ObjC\");"),
			("\t\t\t// enable swift support"),
			("\t\t\tArguments.Add(\"-rpath \\\"/usr/lib/swift\\\"\");"),
			("\t\t\t//Arguments.Add(\"-rpath \\\"@executable_path/Frameworks\\\"\");"),
			("\t\t\t// /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/swift"),
			("\t\t\tString swiftLibPath = String.Format(\"-L {0}/usr/lib/swift\",ToolChainSettings.Value.GetSDKPath(LinkEnvironment.Architecture));"),
			("\t\t\tArguments.Add(swiftLibPath);"),
			("\t\t\tLog.TraceInformation(\"Add swift lib path: {0}\", swiftLibPath);"),
			("\t\t\t// /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx"),
			("\t\t\tswiftLibPath = String.Format(\"-L {0}/../lib/swift/{1}\",Settings.ToolchainDir, \"macosx\");"),
			("\t\t\tArguments.Add(swiftLibPath);"),
			("\t\t\tLog.TraceInformation(\"Add swift lib path : {0}\", swiftLibPath);"),
			("\t\t\t" + insetCodeEndFlag)}
	} else if utility.UEVersionGreaterOrEqual510 {
		insertTargetCodeArray = []string{
			(insetCodeBeginFlag),
			("\t\t\tArguments.Add(\"-ObjC\");"),
			("\t\t\t// enable swift support"),
			("\t\t\tArguments.Add(\"-rpath \\\"/usr/lib/swift\\\"\");"),
			("\t\t\t//Arguments.Add(\"-rpath \\\"@executable_path/Frameworks\\\"\");"),
			("\t\t\t// /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/swift"),
			("\t\t\tString swiftLibPath = String.Format(\"-L {0}/usr/lib/swift\",SDKPath);"),
			("\t\t\tArguments.Add(swiftLibPath);"),
			("\t\t\tLog.TraceInformation(\"Add swift lib path: {0}\", swiftLibPath);"),
			("\t\t\t// //Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx"),
			("\t\t\tswiftLibPath = String.Format(\"-L {0}/../lib/swift/{1}\",Settings.ToolchainDir, \"macosx\");"),
			("\t\t\tArguments.Add(swiftLibPath);"),
			("\t\t\tLog.TraceInformation(\"Add swift lib path : {0}\", swiftLibPath);"),
			("\t\t\t" + insetCodeEndFlag)}
	} else {
		insertTargetCodeArray = []string{
			(insetCodeBeginFlag),
			("\t\t\tResult += \" -ObjC\";"),
			("\t\t\t// enable swift support"),
			("\t\t\tResult += \" -rpath \\\"/usr/lib/swift\\\"\";"),
			("\t\t\t// Result += \" -rpath \\\"@executable_path/Frameworks\\\"\";"),
			("\t\t\t// /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/swift"),
			("\t\t\tString swiftLibPath = String.Format(\" -L {0}/usr/lib/swift\",SDKPath);"),
			("\t\t\tResult += swiftLibPath;"),
			("\t\t\tLog.TraceInformation(\"Add swift lib path: {0}\", swiftLibPath);"),
			("\t\t\t// /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx"),
			("\t\t\tswiftLibPath = String.Format(\" -L {0}../lib/swift/{1}\",Settings.ToolchainDir, \"macosx\");"),
			("\t\t\tResult += swiftLibPath;"),
			("\t\t\tLog.TraceInformation(\"Add swift lib path : {0}\", swiftLibPath);"),
			("\t\t\t" + insetCodeEndFlag)}
	}

	// 插入目标位置下一段关键代码
	matchTargetText := "// Needed to make sure install_name_tool will be able to update paths in Mach-O headers"
	placeholderText := "\t\t\t"
	status, message := InsertUBTSource(ubtFilePath, originalKeyword, insetCodeBeginFlag, insetCodeEndFlag, insertTargetCodeArray, matchTargetText, placeholderText, isAddCode, "")
	return CheckFixUBTResult("MacToolChain:ObjC", status, message)
}

/***********************************************************************************/
/*                 修改Xcode版本号高于13.2.1时，编译报错问题                            */
/*    variable “LayerNames” set but not used [-Werror,-Wunused-but-set-variable]   */
/***********************************************************************************/
func ModifyMacToolChainSource(ueRoot string, isAddCode bool) bool {
	ubtFilePath := filepath.Join(ueRoot, "Source", "Programs", "UnrealBuildTool", "Platform", "Mac", "MacToolChain.cs")

	originalKeyword := "-Wno-unused-but-set-variable"
	insetCodeBeginFlag := "//--------- Modified macOS Buid Error by WMBuild Start"
	insetCodeEndFlag := "//--------- Modified macOS Buid Error by WMBuild END"

	// 需要插入的代码
	insertTargetCodeArray := []string{}
	if utility.UEVersionGreaterOrEqual510 {
		//大于5.1.0的不需要修改,ClangToolChain.cs已支持
		return false
	} else {
		insertTargetCodeArray = []string{
			(insetCodeBeginFlag),
			("\t\t\tif (GetClangVersion() > Version.Parse(\"13.0.0\"))"),
			("\t\t\t{"),
			("\t\t\t\tResult += \" -Wno-unused-but-set-variable\";"),
			("\t\t\t\tResult += \" -Wno-ordered-compare-function-pointers\";"),
			("\t\t\t}"),
			("\t\t\tif (GetClangVersion() > Version.Parse(\"14.0.0\"))"),
			("\t\t\t{"),
			("\t\t\t\tResult += \" -Wno-error=bitwise-instead-of-logical\";"),
			("\t\t\t}"),
			("\t\t\tif (GetClangVersion() >= Version.Parse(\"15.0.0\"))"),
			("\t\t\t{"),
			("\t\t\t\tResult += \" -Wno-deprecated-builtins\";"),
			("\t\t\t\tResult += \" -Wno-single-bit-bitfield-constant-conversion\";"),
			("\t\t\t}"),
			("\t\t\t" + insetCodeEndFlag)}
	}

	//插入目标位置下一段关键代码
	matchTargetText := "// Create DWARF format debug info if wanted"
	placeholderText := "\t\t\t"
	status, message := InsertUBTSource(ubtFilePath, originalKeyword, insetCodeBeginFlag, insetCodeEndFlag, insertTargetCodeArray, matchTargetText, placeholderText, isAddCode, "")
	return CheckFixUBTResult("MacToolChain:CompileArg", status, message)
}
