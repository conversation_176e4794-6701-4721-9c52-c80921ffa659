package ubt

import (
	"onetools/cmd/utility"
	"path/filepath"
	"strings"
)

/*********************************************************************/
/*             修改UEDeployIOS.cs，对应Info.plist                     */
/*********************************************************************/
func ModifyNotificationGameCenter(ueRoot string, isAddCode bool) bool {
	ubtFilePath := filepath.Join(ueRoot, "Source", "Programs", "UnrealBuildTool", "Platform", "IOS", "UEDeployIOS.cs")
	utility.UnrealEngineVersion = ""
	originalKeyword := "<string>gamekit</string>"
	insetCodeBeginFlag := "//--------- Modified Infoplist by WMBuild Start"
	insetCodeEndFlag := "//--------- Modified Infoplist by WMBuild END"

	audioSupportedStr := "\t\t\t"
	if utility.UEVersionGreaterOrEqual520 {
		audioSupportedStr = ("\t\t\tbBackgroundAudioSupported |= bEnableAudio;")
	}

	addGamekitStr := ("\t\t\t\tRequiredCaps += \"\\t\\t<string>gamekit</string>\\n\";")
	if utility.UEVersionGreaterOrEqual530 {
		addGamekitStr = ("\t\t\t\tRequiredCaps.Add(\"<string>gamekit</string>\");")
	}
	// 需要插入的代码
	insertTargetCodeArray := []string{
		(insetCodeBeginFlag),
		("\t\t\tbool bEnableRemoteNotifications = false;"),
		("\t\t\tIni.GetBool(\"/Script/IOSConfig.IOSConfigSettings\", \"bEnableRemoteNotifications\", out bEnableRemoteNotifications);"),
		("\t\t\tif(bEnableRemoteNotifications)"),
		("\t\t\t{"),
		("\t\t\t\tbRemoteNotificationsSupported = true;"),
		("\t\t\t}"),
		("\t\t\tbool bEnableGameCenter = false;"),
		("\t\t\tIni.GetBool(\"/Script/IOSConfig.IOSConfigSettings\", \"bEnableGameCenter\", out bEnableGameCenter);"),
		("\t\t\tbool bEnableGameCenterSupport = false;"),
		("\t\t\tIni.GetBool(\"/Script/IOSRuntimeSettings.IOSRuntimeSettings\", \"bEnableGameCenterSupport\", out bEnableGameCenterSupport);"),
		("\t\t\tif(bEnableGameCenter || bEnableGameCenterSupport)"),
		("\t\t\t{"),
		addGamekitStr,
		("\t\t\t}"),
		("\t\t\tbool bEnableAudio = false;"),
		("\t\t\tIni.GetBool(\"/Script/IOSConfig.IOSConfigSettings\", \"bEnableAudio\", out bEnableAudio);"),
		audioSupportedStr,
		("\t\t\t" + insetCodeEndFlag)}

	//插入目标位置下一段关键代码
	matchTargetText := "// generate the plist file(\\s)*StringBuilder Text = new StringBuilder\\(\\);"
	placeholderText := "\t\t\t"
	status, message := InsertUBTSource(ubtFilePath, originalKeyword, insetCodeBeginFlag, insetCodeEndFlag, insertTargetCodeArray, matchTargetText, placeholderText, isAddCode, "")
	return CheckFixUBTResult("DeployIOS:Notification", status, message)
}

/*********************************************************************/
/*              修改UEDeployIOS.cs，对UPL按文件名升序排列                */
/*********************************************************************/
func ModifyUPLSort(ueRoot string, isAddCode bool) bool {
	ubtFilePath := filepath.Join(ueRoot, "Source", "Programs", "UnrealBuildTool", "Platform", "IOS", "UEDeployIOS.cs")

	originalKeyword := "Results.OrderBy(item => item.Value.Split('/').Last());"
	insetCodeBeginFlag := "//--------- Modified UPL Sort by WMBuild Start"
	insetCodeEndFlag := "//--------- Modified UPL Sort by WMBuild END"

	// 需要插入的代码
	insertTargetCodeArray := []string{
		(insetCodeBeginFlag),
		("\t\t\tResults = Results.OrderBy(item => item.Value.Split('/').Last());"),
		("\t\t\tLog.TraceInformation(\"WMSDK sort UPL by filename in ascending order...\");"),
		("\t\t\t" + insetCodeEndFlag)}

	//插入目标位置下一段关键代码
	matchTargetText := "foreach\\(ReceiptProperty Property in Results\\)"
	placeholderText := "\t\t\t"
	status, message := InsertUBTSource(ubtFilePath, originalKeyword, insetCodeBeginFlag, insetCodeEndFlag, insertTargetCodeArray, matchTargetText, placeholderText, isAddCode, "")
	return CheckFixUBTResult("DeployIOS:UPLSort", status, message)
}

/*********************************************************************/
/*      修改UEDeployIOS.cs，对位置权限描述增加是否为空判断，防止审核被拒     */
/*********************************************************************/
func ModifyLocationDescription(ueRoot string, isAddCode bool) bool {
	ubtFilePath := filepath.Join(ueRoot, "Source", "Programs", "UnrealBuildTool", "Platform", "IOS", "UEDeployIOS.cs")

	originalKeyword := "if (!string.IsNullOrWhiteSpace(LocationAlwaysUsageDescription))"
	insetCodeBeginFlag := "//--------- Modified Location Description by WMBuild Start"
	insetCodeEndFlag := "//--------- Modified Location Description by WMBuild END"

	// 需要插入的代码
	insertTargetCodeArray := []string{
		(insetCodeBeginFlag),
		("\t\t\tif (!string.IsNullOrWhiteSpace(LocationAlwaysUsageDescription))"),
		("\t\t\t{"),
		("\t\t\t\tText.AppendLine(\"\\t<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>\");"),
		("\t\t\t\tText.AppendLine(string.Format(\"\\t<string>{0}</string>\", LocationAlwaysUsageDescription));"),
		("\t\t\t}"),
		("\t\t\tif (!string.IsNullOrWhiteSpace(LocationWhenInUseDescription))"),
		("\t\t\t{"),
		("\t\t\t\tText.AppendLine(\"\\t<key>NSLocationWhenInUseUsageDescription</key>\");"),
		("\t\t\t\tText.AppendLine(string.Format(\"\\t<string>{0}</string>\", LocationWhenInUseDescription));"),
		("\t\t\t}"),
		("\t\t\t" + insetCodeEndFlag)}

	//插入目标位置下一段关键代码
	matchTargetText := ""
	if isAddCode {
		matchTargetText = "Text.AppendLine.*NSLocationAlwaysAndWhenInUseUsageDescription.*\\s*Text.AppendLine.*LocationAlwaysUsageDescription.*\\s*Text.AppendLine.*NSLocationWhenInUseUsageDescription.*\\s*Text.AppendLine.*LocationWhenInUseDescription\\)\\);"
	} else {
		matchTargetCodeArray := []string{
			("Text.AppendLine(\"\\t<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>\");"),
			("\t\t\tText.AppendLine(string.Format(\"\\t<string>{0}</string>\", LocationAlwaysUsageDescription));"),
			("\t\t\tText.AppendLine(\"\\t<key>NSLocationWhenInUseUsageDescription</key>\");"),
			("\t\t\tText.AppendLine(string.Format(\"\\t<string>{0}></string>\", LocationWhenInUseDescription));")}
		matchTargetText = strings.Join(matchTargetCodeArray, "\n")
	}
	placeholderText := "\t\t\t"
	status, message := ReplaceUBTSource(ubtFilePath, originalKeyword, insetCodeBeginFlag, insetCodeEndFlag, insertTargetCodeArray, matchTargetText, placeholderText, isAddCode, "")
	return CheckFixUBTResult("DeployIOS:LocationDesc", status, message)
}

/*********************************************************************/
/*      修改UEDeployIOS.cs，添加Audio Capability key值                 */
/*********************************************************************/
func ModifyAudioCapability(ueRoot string, isAddCode bool) bool {
	if utility.UEVersionGreaterOrEqual520 {
		return false
	}
	//修改BackgroundModes判断条件
	needBuildUBT := replaceBackgroundModesCondition(ueRoot, isAddCode)
	//增加audio key设置
	needBuildUBT = insertAudioCapabilityKey(ueRoot, isAddCode) || needBuildUBT

	return needBuildUBT
}

func insertAudioCapabilityKey(ueRoot string, isAddCode bool) bool {
	ubtFilePath := filepath.Join(ueRoot, "Source", "Programs", "UnrealBuildTool", "Platform", "IOS", "UEDeployIOS.cs")

	originalKeyword := "<string>audio</string>"
	insetCodeBeginFlag := "//--------- Modified enable Audio key by WMBuild Start"
	insetCodeEndFlag := "//--------- Modified enable Audio key by WMBuild END"

	// 需要插入的代码
	insertTargetCodeArray := []string{
		(insetCodeBeginFlag),
		("\t\t\t\tif (bEnableAudio)"),
		("\t\t\t\t{"),
		("\t\t\t\t\tText.AppendLine(\"\\t\\t<string>audio</string>\");"),
		("\t\t\t\t}"),
		("\t\t\t\t" + insetCodeEndFlag)}

	//插入目标位置下一段关键代码
	matchTargetText := "if \\(bBackgroundFetch\\)"
	placeholderText := "\t\t\t\t"
	status, message := InsertUBTSource(ubtFilePath, originalKeyword, insetCodeBeginFlag, insetCodeEndFlag, insertTargetCodeArray, matchTargetText, placeholderText, isAddCode, "")
	return CheckFixUBTResult("DeployIOS:AudioKey", status, message)
}

func replaceBackgroundModesCondition(ueRoot string, isAddCode bool) bool {
	ubtFilePath := filepath.Join(ueRoot, "Source", "Programs", "UnrealBuildTool", "Platform", "IOS", "UEDeployIOS.cs")

	originalKeyword := "|| bEnableAudio"
	insetCodeBeginFlag := "//--------- Modified background modes condition by WMBuild Start"
	insetCodeEndFlag := "//--------- Modified background modes condition by WMBuild END"

	// 需要插入的代码
	insertTargetCodeArray := []string{
		(insetCodeBeginFlag),
		("\t\t\tif (bRemoteNotificationsSupported || bBackgroundFetch || bEnableAudio)"),
		("\t\t\t" + insetCodeEndFlag)}

	//插入目标位置下一段关键代码
	matchTargetText := ""
	if isAddCode {
		matchTargetText = "if \\(bRemoteNotificationsSupported \\|\\| bBackgroundFetch\\)"
	} else {
		matchTargetText = "if (bRemoteNotificationsSupported || bBackgroundFetch)"
	}

	placeholderText := "\t\t\t"
	status, message := ReplaceUBTSource(ubtFilePath, originalKeyword, insetCodeBeginFlag, insetCodeEndFlag, insertTargetCodeArray, matchTargetText, placeholderText, isAddCode, "")
	return CheckFixUBTResult("DeployIOS:AudioCondition", status, message)
}
