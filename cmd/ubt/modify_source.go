package ubt

import (
	"io/ioutil"
	"log"
	"onetools/cmd/utility"
	"os"
	"regexp"
	"strings"
)

/*********************************************************************/
/*                      对UBT进行插入、删除操作                         */
/*********************************************************************/
// 对指定UBT源码进行插入、删除操作
// 参数
// 		ubtFilePath：UBT文件路径
// 		originalKeyword：要插入文本的部分关键内容，有可能已经手动修改过了，用来查找是否已添加了
// 		insetCodeBeginFlag：插入文本内容的起始标识
// 		insetCodeEndFlag：插入文本内容的结束标识
// 		inserSourcCodeBuilder：需要插入的完整文本内容
// 		matchTargetText：插入目标位置的关键文本，用来查找确定插入位置，会插入在该文本内容前面
// 		placeholderText：占位符文本，需要几个缩进(\t)，为保持代码风格一致
// 		isAddCode：true:插入代码  false:删除已插入的代码
// 		beginMatchText：查找插入的关键位置时，从哪个文本开始查找(有可能matchTargetText有多个，用来更精确查找)
// 返回值:
//		是否修改了UBT文件

type FixStatus int

const (
	Unchanged     FixStatus = iota //不需要改动
	InsertSuccess                  //添加成功
	InsertFailure                  //添加失败
	UpdateSuccess                  //更新成功
	UpdateFailure                  //更新失败
	RemoveSuccess                  //移除成功
	RemoveFailure                  //移除失败
	AbnormalExit                   //异常退出
)

func InsertUBTSource(ubtFilePath string,
	originalKeyword string,
	insetCodeBeginFlag string,
	insetCodeEndFlag string,
	inserSourcCodeArray []string,
	matchTargetText string,
	placeholderText string,
	isAddCode bool,
	beginMatchText string) (FixStatus, string) {

	if !utility.IsExist(ubtFilePath) {
		return AbnormalExit, "未找到要修改的UBT文件"
	}

	lineBytes, err := ioutil.ReadFile(ubtFilePath)
	// var lines []string
	if err != nil {
		return AbnormalExit, "要修改的UBT文件读取失败"
	}
	placeholderContent := "\n" + placeholderText
	sourceContents := string(lineBytes)
	// 根据关键字判断是否已添加了目标代码
	isIn := strings.Contains(sourceContents, originalKeyword)
	newSourceContent := ""
	insertTargetCode := strings.Join(inserSourcCodeArray, "\n")
	var fixStatus FixStatus = Unchanged
	fixMessage := ""

	if isIn {
		// 已添加了目标代码，检查是否需要更新
		matchBeginExpression := insetCodeBeginFlag
		beginRegexp, _ := regexp.Compile(matchBeginExpression)
		// 查找插入代码起始标记
		beginFlagMatch := beginRegexp.FindStringIndex(sourceContents)
		if beginFlagMatch != nil {
			matchEndExpression := "(\\s){0,}" + insetCodeEndFlag
			endRegexp, _ := regexp.Compile(matchEndExpression)
			// 查找插入代码起始标记
			endFlagMatch := endRegexp.FindStringIndex(sourceContents)
			if endFlagMatch != nil {
				oldInsertCode := sourceContents[beginFlagMatch[0]:endFlagMatch[1]]
				if isAddCode {
					// 添加targtcode
					if oldInsertCode == insertTargetCode {
						// log.Printf("已使用该工具添加了【%s】，不需要更新\n", originalKeyword)
						fixMessage = "已是最新"
					} else {
						// log.Printf("已使用该工具添加了【%s】，并需要更新\n", originalKeyword)
						newSourceContent = strings.Replace(sourceContents, oldInsertCode, insertTargetCode, -1)
						fixStatus = UpdateSuccess
					}
				} else {
					// 删除targtcode
					// log.Printf("已使用该工具添加了【%s】，并进行删除\n", originalKeyword)
					newSourceContent = strings.Replace(sourceContents, oldInsertCode+placeholderContent, "", -1)
					fixStatus = RemoveSuccess
				}
			} else {

				// log.Printf(" 更新【%s】出现异常，未匹配到结尾关键信息\n", originalKeyword)
				fixStatus = InsertFailure
				fixMessage = "未匹配到结尾关键信息"
			}
		} else {
			// log.Printf(" 不是使用该工具添加的【%s】，跳过不处理\n", originalKeyword)
			fixMessage = "跳过，不处理"
		}

	} else if isAddCode {
		// log.Printf("  开始插入:【%s】, Keyword:【%s】\n", originalKeyword, matchTargetText)
		tempSourceContents := sourceContents
		searchBeginIndex := 0
		if beginMatchText != "" {
			searchBeginRegexp, _ := regexp.Compile(beginMatchText)
			// 查找插入代码起始标记
			searchBeginMatch := searchBeginRegexp.FindStringIndex(sourceContents)
			if searchBeginMatch != nil {
				searchBeginIndex = searchBeginMatch[0]
				tempSourceContents = sourceContents[searchBeginIndex:]
			}
		}
		targetRegexp, _ := regexp.Compile(matchTargetText)
		// 查找插入代码起始标记
		keyWordMatch := targetRegexp.FindStringIndex(tempSourceContents)
		if keyWordMatch != nil {
			inserIndex := searchBeginIndex + keyWordMatch[0]
			newSourceContent = sourceContents[:inserIndex] + insertTargetCode + placeholderContent + sourceContents[inserIndex:]
			fixStatus = InsertSuccess
		} else {
			fixStatus = InsertFailure
			fixMessage = "未找到关键文本"
		}
	} else {
		// log.Printf("未匹配到关键文本:【%s】, 不需要处理\n", originalKeyword)
		fixStatus = Unchanged
		fixMessage = "未匹配到关键文本,不处理"
	}
	if newSourceContent != "" {
		os.Chmod(ubtFilePath, 0777)
		file, err := os.OpenFile(ubtFilePath, os.O_RDWR|os.O_TRUNC, 0666)
		if err != nil {
			// log.Println("文件打开失败:", ubtFilePath, "\nerror:", err)
			fixStatus = fixStatus + 1
			fixMessage = "UBT要写入时打开文件异常"
			return fixStatus, fixMessage
		}

		defer file.Close() // 关闭文件
		_, err = file.WriteString(newSourceContent)
		if err != nil {
			// log.Println("文件写入失败:", ubtFilePath, "\nerror:", err)
			fixStatus = fixStatus + 1
			fixMessage = "UBT文件写入异常"
			return fixStatus, fixMessage
		}
		return fixStatus, fixMessage
	}
	return fixStatus, fixMessage
}

/*********************************************************************/
/*                      对UBT进行替换操作                         */
/*********************************************************************/
// 对指定UBT源码进行替换操作
// 参数
// 		ubtFilePath：UBT文件路径
// 		originalKeyword：要插入文本的部分关键内容，有可能已经手动修改过了，用来查找是否已添加了
// 		insetCodeBeginFlag：插入文本内容的起始标识
// 		insetCodeEndFlag：插入文本内容的结束标识
// 		inserSourcCodeBuilder：需要插入的完整文本内容
// 		matchTargetText：被替换的完整文本内容
// 		placeholderText：占位符文本，需要几个缩进(\t)，为保持代码风格一致
// 		isAddCode：true:插入代码  false:删除已插入的代码
// 		beginMatchText：查找插入的关键位置时，从哪个文本开始查找(有可能matchTargetText有多个，用来更精确查找)
// 返回值:
//		是否修改了UBT文件

func ReplaceUBTSource(ubtFilePath string,
	originalKeyword string,
	insetCodeBeginFlag string,
	insetCodeEndFlag string,
	inserSourcCodeArray []string,
	matchTargetText string,
	placeholderText string,
	isAddCode bool,
	beginMatchText string) (FixStatus, string) {

	if !utility.IsExist(ubtFilePath) {
		return AbnormalExit, "未找到要修改的UBT文件"
	}

	lineBytes, err := ioutil.ReadFile(ubtFilePath)
	// var lines []string
	if err != nil {
		return AbnormalExit, "要修改的UBT文件读取失败"
	}

	sourceContents := string(lineBytes)
	// 根据关键字判断是否已添加了目标代码
	isIn := strings.Contains(sourceContents, originalKeyword)
	newSourceContent := ""
	insertTargetCode := strings.Join(inserSourcCodeArray, "\n")
	var fixStatus FixStatus = Unchanged
	fixMessage := ""
	if isIn {
		// log.Printf("已经替换过【%s】，检查是否需要更新\n", originalKeyword)
		// 已添加了目标代码，检查是否需要更新
		matchBeginExpression := insetCodeBeginFlag
		beginRegexp, _ := regexp.Compile(matchBeginExpression)
		// 查找插入代码起始标记
		beginFlagMatch := beginRegexp.FindStringIndex(sourceContents)
		if beginFlagMatch != nil {
			matchEndExpression := "(\\s){0,}" + insetCodeEndFlag
			endRegexp, _ := regexp.Compile(matchEndExpression)
			// 查找插入代码起始标记
			endFlagMatch := endRegexp.FindStringIndex(sourceContents)
			if endFlagMatch != nil {
				oldInsertCode := sourceContents[beginFlagMatch[0]:endFlagMatch[1]]
				if isAddCode {
					// 添加targtcode
					if oldInsertCode == insertTargetCode {
						// log.Printf("已使用该工具添加了【%s】，不需要更新\n", originalKeyword)
						fixMessage = "已是最新"
					} else {
						// log.Printf("已使用该工具添加了【%s】，并需要更新\n", originalKeyword)
						newSourceContent = strings.Replace(sourceContents, oldInsertCode, insertTargetCode, -1)
						fixStatus = UpdateSuccess
					}
				} else {
					// 还原targtcode
					// log.Printf("已使用该工具添加了【%s】，并进行删除\n", originalKeyword)
					newSourceContent = strings.Replace(sourceContents, oldInsertCode, matchTargetText, -1)
					fixStatus = RemoveSuccess
				}
			} else {
				// log.Printf(" 更新【%s】出现异常，未匹配到结尾关键信息\n", originalKeyword)
				fixStatus = InsertFailure
				fixMessage = "未匹配到结尾关键信息"
			}
		} else {
			// log.Printf(" 不是使用该工具添加的【%s】，跳过不处理\n", originalKeyword)
			fixMessage = "跳过，不处理"
		}

	} else if isAddCode {
		// log.Printf("  开始替换:【%s】, Keyword:【%s】\n", originalKeyword, matchTargetText)
		tempSourceContents := sourceContents
		searchBeginIndex := 0
		if beginMatchText != "" {
			searchBeginRegexp, _ := regexp.Compile(beginMatchText)
			// 查找插入代码起始标记
			searchBeginMatch := searchBeginRegexp.FindStringIndex(sourceContents)
			if searchBeginMatch != nil {
				searchBeginIndex = searchBeginMatch[0]
				tempSourceContents = sourceContents[searchBeginIndex:]
			}
		}
		targetRegexp, _ := regexp.Compile(matchTargetText)
		// 查找插入代码起始标记
		keyWordMatch := targetRegexp.FindStringIndex(tempSourceContents)
		if keyWordMatch != nil {
			beginIndex := searchBeginIndex + keyWordMatch[0]
			endIndex := searchBeginIndex + keyWordMatch[1]
			oldCodeContent := sourceContents[beginIndex:endIndex]
			newSourceContent = strings.Replace(sourceContents, oldCodeContent, insertTargetCode, -1)
			fixStatus = InsertSuccess
		} else {
			// log.Printf("替换:【%s】失败, 未找到关键文本:【%s】\n", originalKeyword, matchTargetText)
			fixStatus = InsertFailure
			fixMessage = "未找到关键文本"
		}
	} else {
		// log.Printf("未匹配到关键文本:【%s】, 不需要处理\n", originalKeyword)
		fixStatus = Unchanged
		fixMessage = "未匹配到关键文本,不处理"

	}
	if newSourceContent != "" {
		os.Chmod(ubtFilePath, 0777)
		file, err := os.OpenFile(ubtFilePath, os.O_RDWR|os.O_TRUNC, 0666)
		if err != nil {
			// log.Println("文件打开失败:", ubtFilePath, "\nerror:", err)
			fixStatus = fixStatus + 1
			fixMessage = "UBT要写入时打开文件异常"
			return fixStatus, fixMessage
		}
		defer file.Close() // 关闭文件
		_, err = file.WriteString(newSourceContent)
		if err != nil {
			// log.Println("文件写入失败:", ubtFilePath, "\nerror:", err)
			fixStatus = fixStatus + 1
			fixMessage = "UBT文件写入异常"
			return fixStatus, fixMessage
		}
		return fixStatus, fixMessage
	}
	return fixStatus, fixMessage
}

func CheckFixUBTResult(patchType string, status FixStatus, message string) bool {

	result := false
	fixType := ""   //修改类型
	fixResult := "" //修改结果

	if status == InsertSuccess {
		fixType = "添加"
		fixResult = "√"
		result = true
	} else if status == InsertFailure {
		fixType = "添加"
		fixResult = "ㄨ"
	} else if status == UpdateSuccess {
		fixType = "更新"
		fixResult = "√"
		result = true
	} else if status == UpdateFailure {
		fixType = "更新"
		fixResult = "ㄨ"
	} else if status == RemoveSuccess {
		fixType = "撤销"
		fixResult = "√"
		result = true
	} else if status == RemoveFailure {
		fixType = "撤销"
		fixResult = "ㄨ"
	} else if status == AbnormalExit {
		fixType = "修改"
		fixResult = "ㄨ"
	} else {
		fixType = "本地未修改"
		fixResult = "-"
	}
	log.Printf("【%s-%s】:%s %s\n", patchType, fixType, fixResult, message)
	return result
}
