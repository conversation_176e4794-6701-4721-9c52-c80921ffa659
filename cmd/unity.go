package cmd

import (
	"fmt"
	"onetools/cmd/unity"

	"github.com/spf13/cobra"
)

var unityCmd = &cobra.Command{
	Use:   "unity",
	Short: "Unity组件管理",
	Long:  `用于管理SDK提供的Unity组件`,
}

func init() {
	rootCmd.AddCommand(unityCmd)

	// (1) 查询插件列表
	listCmd := &cobra.Command{
		Use:   "list",
		Short: "查询插件列表",
		Run: func(cmd *cobra.Command, args []string) {
			env, _ := cmd.Flags().GetString("env")
			if env == "" {
				env = "release"
			}
			var nexusPath string
			if env == "debug" {
				nexusPath = "ios-snapshot/com/pwrd/u3d/"
			} else {
				nexusPath = "ios-release/com/pwrd/u3d/"
			}
			list, err := unity.CheckUnityNameList(nexusPath)
			if err != nil {
				fmt.Println("查询失败:", err)
				return
			}
			fmt.Println("插件列表:")
			for _, name := range list {
				fmt.Println(name)
			}
		},
	}
	listCmd.Flags().StringP("env", "e", "release", "环境，release或debug")
	unityCmd.AddCommand(listCmd)

	// (2) 查询某插件可用版本
	versionsCmd := &cobra.Command{
		Use:   "versions",
		Short: "查询某插件可用版本",
		Run: func(cmd *cobra.Command, args []string) {
			name, _ := cmd.Flags().GetString("name")
			env, _ := cmd.Flags().GetString("env")
			if env == "" {
				env = "release"
			}
			var realEnv string
			if env == "debug" {
				realEnv = "ios-snapshot"
			} else {
				realEnv = "ios-release"
			}
			versions, err := unity.CheckUnityPluginVersionList(name, realEnv)
			if err != nil {
				fmt.Println("查询失败:", err)
				return
			}
			fmt.Printf("插件[%s] 可用版本列表：\n", name)
			for _, v := range versions {
				fmt.Println(v)
			}
		},
	}
	versionsCmd.Flags().StringP("name", "n", "", "插件名称")
	versionsCmd.Flags().StringP("env", "e", "release", "环境，release或debug")
	unityCmd.AddCommand(versionsCmd)

	// (3) 安装或升级插件
	installCmd := &cobra.Command{
		Use:   "install",
		Short: "安装或升级插件",
		Long:  "安装或升级指定的Unity插件",
		Run: func(cmd *cobra.Command, args []string) {
			name, _ := cmd.Flags().GetString("name")
			version, _ := cmd.Flags().GetString("version")
			project, _ := cmd.Flags().GetString("project")
			env, _ := cmd.Flags().GetString("env")
			if env == "" {
				env = "release"
			}
			var realEnv string
			if env == "debug" {
				realEnv = "ios-snapshot"
			} else {
				realEnv = "ios-release"
			}
			// 如果是snapshot，版本号后拼接-SNAPSHOT
			if realEnv == "ios-snapshot" && version != "" {
				version += "-SNAPSHOT"
			}
			err := unity.AutoInstallUnityPlugin(name, version, project)
			if err != nil {
				fmt.Println("安装或升级失败:", err)
			} else {
				fmt.Println("安装或升级成功！")
			}
		},
	}
	installCmd.Flags().StringP("name", "n", "", "插件名称")
	installCmd.Flags().StringP("version", "v", "", "插件版本号")
	installCmd.Flags().StringP("project", "p", "", "目标工程路径")
	installCmd.Flags().StringP("env", "e", "release", "环境，release或debug")
	unityCmd.AddCommand(installCmd)

	// (4) 卸载插件
	uninstallCmd := &cobra.Command{
		Use:   "uninstall",
		Short: "卸载插件",
		Run: func(cmd *cobra.Command, args []string) {
			name, _ := cmd.Flags().GetString("name")
			version, _ := cmd.Flags().GetString("version")
			project, _ := cmd.Flags().GetString("project")
			env, _ := cmd.Flags().GetString("env")
			if env == "" {
				env = "release"
			}
			// 如果是debug，版本号后拼接-SNAPSHOT
			if env == "debug" && version != "" {
				version += "-SNAPSHOT"
			}
			err := unity.UninstallUnityPackageTxt(project, name, version)
			if err != nil {
				fmt.Println("卸载失败:", err)
			} else {
				fmt.Println("卸载成功！")
			}
		},
	}
	uninstallCmd.Flags().StringP("name", "n", "", "插件名称")
	uninstallCmd.Flags().StringP("version", "v", "", "插件版本号")
	uninstallCmd.Flags().StringP("project", "p", "", "目标工程路径")
	uninstallCmd.Flags().StringP("env", "e", "release", "环境，release或debug")
	unityCmd.AddCommand(uninstallCmd)

	// (5) 通过mobileProvision文件生成打包物料文件夹
	generateBuildCmd := &cobra.Command{
		Use:   "generatebuild",
		Short: "通过mobileProvision文件生成打包物料文件夹",
		Run: func(cmd *cobra.Command, args []string) {
			name, _ := cmd.Flags().GetString("name")
			mobileprovision, _ := cmd.Flags().GetString("mobileprovision")
			project, _ := cmd.Flags().GetString("project")
			err := unity.AutoCreatePackageFolder(project, name, mobileprovision)
			if err != nil {
				fmt.Println("生成打包物料文件夹失败:", err)
			} else {
				fmt.Println("生成打包物料文件夹成功！")
			}
		},
	}
	generateBuildCmd.Flags().StringP("name", "n", "", "打包物料文件夹名称")
	generateBuildCmd.Flags().StringP("mobileprovision", "m", "", "mobileprovision文件路径")
	generateBuildCmd.Flags().StringP("project", "p", "", "目标工程路径")
	unityCmd.AddCommand(generateBuildCmd)

	// (6) 查询已安装插件列表
	listInstalledCmd := &cobra.Command{
		Use:   "listinstalled",
		Short: "查询指定项目已安装的unity插件列表",
		Run: func(cmd *cobra.Command, args []string) {
			project, _ := cmd.Flags().GetString("project")
			unity.PrintInstallPluginList(project)
		},
	}
	listInstalledCmd.Flags().StringP("project", "p", "", "目标工程路径")
	unityCmd.AddCommand(listInstalledCmd)
}
