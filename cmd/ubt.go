/*
Copyright © 2022 NAME HERE <EMAIL ADDRESS>
*/
package cmd

import (
	"fmt"
	"log"
	"onetools/cmd/ubt"
	"onetools/cmd/utility"
	"path/filepath"
	"runtime"

	"github.com/spf13/cobra"
)

var UEInstallPath string

// ubtCmd represents the ubt command
var ubtCmd = &cobra.Command{
	Use:   "ubt",
	Short: "修改UBT源码，以支持各平台部分功能",
	Long: `
修改UBT源码，以支持iOS、Mac平台部分功能，
iOS：主要支持Sign in with Apple、Push、UniversalLink及Swift链接编译。
Mac：编译链接参数修改。

e.g. 修改iOS平台: onetools ubt -e "D:\UnrealEngine\Engine" -f -i
e.g. 撤销iOS平台修改: onetools ubt -e "D:\UnrealEngine\Engine" -r -i

e.g. 修改Mac平台: onetools ubt -e "D:\UnrealEngine\Engine" -f -m
e.g. 撤销Mac平台修改: onetools ubt -e "D:\UnrealEngine\Engine" -r -m

`,
	Run: func(cmd *cobra.Command, args []string) {
		if UEInstallPath == "" {
			panic("请设置Unreal Engine安装目录， e.g.:/Users/<USER>/Epic Games/UE_4.27/Engine/")
		}

		UEInstallPath = utility.CheckEnginePath(UEInstallPath)
		//获取unrealEngine版本号
		utility.GetUnrealEngineVersion(UEInstallPath)
		utility.UEVersionGreaterOrEqual500 = utility.GreaterOrEqualCompareVersion(utility.UnrealEngineVersion, "5.0.0")
		utility.UEVersionGreaterOrEqual510 = utility.GreaterOrEqualCompareVersion(utility.UnrealEngineVersion, "5.1.0")
		utility.UEVersionGreaterOrEqual520 = utility.GreaterOrEqualCompareVersion(utility.UnrealEngineVersion, "5.2.0")
		utility.UEVersionGreaterOrEqual530 = utility.GreaterOrEqualCompareVersion(utility.UnrealEngineVersion, "5.3.0")
		utility.UEVersionGreaterOrEqual540 = utility.GreaterOrEqualCompareVersion(utility.UnrealEngineVersion, "5.4.0")
		log.Printf("unreal engine path : 【%s】,version : 【%s】", UEInstallPath, utility.UnrealEngineVersion)

		isBuild, _ := cmd.Flags().GetBool("build")
		isFix, _ := cmd.Flags().GetBool("fix")
		isRestore, _ := cmd.Flags().GetBool("restore")
		if isFix && isRestore {
			panic("fix和restore不能同时设置")
		}

		// 确定对UBT是添加还是还原操作
		isAddCode := true
		if isRestore {
			isAddCode = false
		}

		needBuildUBT := false
		fixIOS, _ := cmd.Flags().GetBool("ios")
		if fixIOS {
			needBuildUBT = ubt.ModifyNotificationGameCenter(UEInstallPath, isAddCode) || needBuildUBT
			needBuildUBT = ubt.ModifyLocationDescription(UEInstallPath, isAddCode) || needBuildUBT
			needBuildUBT = ubt.ModifyIOSExportsSource(UEInstallPath, isAddCode) || needBuildUBT
			needBuildUBT = ubt.ModifyIOSToolChainSource(UEInstallPath, isAddCode) || needBuildUBT
			needBuildUBT = ubt.ModifyIOSToolChainCompileArguments(UEInstallPath, isAddCode) || needBuildUBT
			needBuildUBT = ubt.ModifyXcodeProjectSource(UEInstallPath, isAddCode) || needBuildUBT
			needBuildUBT = ubt.ModifyResourceExtensions(UEInstallPath, isAddCode) || needBuildUBT
			needBuildUBT = ubt.ModifyAudioCapability(UEInstallPath, isAddCode) || needBuildUBT
			// ubt.ModifyUPLSort("", false)
		}

		fixAndroid, _ := cmd.Flags().GetBool("android")
		if fixAndroid {
			needBuildUBT = ubt.ModifyAndroidDeploySource(UEInstallPath, isAddCode) || needBuildUBT
			needBuildUBT = ubt.ReplaceGameApplicationSource(UEInstallPath, isAddCode) || needBuildUBT
		}

		fixMac, _ := cmd.Flags().GetBool("mac")
		if fixMac {
			needBuildUBT = ubt.ModifyCompileArgumentsObjC(UEInstallPath, isAddCode) || needBuildUBT
			needBuildUBT = ubt.ModifyMacToolChainSource(UEInstallPath, isAddCode) || needBuildUBT
		}

		if needBuildUBT || isBuild {
			if runtime.GOOS == "windows" {
				log.Printf("UBT修改完成，请使用msbuild重新编译UBT!!!!!")
			} else {
				// Engine/Source/Programs/UnrealBuildTool/UnrealBuildTool.csproj
				ueProjectPath := filepath.Join(UEInstallPath, "Source", "Programs", "UnrealBuildTool", "UnrealBuildTool.csproj")
				if utility.IsExist(ueProjectPath) {
					log.Println("准备重新编译UBT，请等待...")
					ubtCMD := ""
					if utility.UEVersionGreaterOrEqual500 {
						ubtCMD = fmt.Sprintf("%s\"%s\"", "sudo dotnet build ", ueProjectPath)
					} else {
						ubtCMD = fmt.Sprintf("%s\"%s\"", "sudo msbuild ", ueProjectPath)
					}
					out := utility.ExecuteCommand("", ubtCMD)
					// 输出日志信息
					log.Println(out)

					if utility.UEVersionGreaterOrEqual500 {
						//UE5.0后，需要将UnrealBuildTool.dll拷贝到DotNET/AutomationTool目录下
						ubtProductFiles := []string{"UnrealBuildTool", "UnrealBuildTool.dll", "UnrealBuildTool.pdb"}
						for _, fileName := range ubtProductFiles {
							ubtDotnetPath := filepath.Join(UEInstallPath, "Binaries", "DotNET", "UnrealBuildTool", fileName)
							uatDotnetPath := filepath.Join(UEInstallPath, "Binaries", "DotNET", "AutomationTool", fileName)
							if utility.IsExist(ubtDotnetPath) {
								utility.FileCopy(ubtDotnetPath, uatDotnetPath)
							}
						}
					}

				} else {
					log.Printf("UBT修改完成，未找到UnrealBuildTool.csproj文件，请使用msbuild命令重新编译UBT!!!!!")
				}
			}
		}
	},
}

func init() {
	rootCmd.AddCommand(ubtCmd)

	// Here you will define your flags and configuration settings.

	// Cobra supports Persistent Flags which will work for this command
	// and all subcommands, e.g.:
	// ubtCmd.PersistentFlags().String("foo", "", "A help for foo")

	// Cobra supports local flags which will only run when this command
	// is called directly, e.g.:
	// ubtCmd.Flags().BoolP("toggle", "t", false, "Help message for toggle")

	ubtCmd.Flags().StringVarP(&UEInstallPath, "engine", "e", "", `【必选】引擎Engine目录绝对路径
 e.g.:/Users/<USER>/Epic Games/UE_4.27/Engine/`)
	ubtCmd.MarkFlagRequired("engine")
	ubtCmd.Flags().BoolP("fix", "f", false, "【可选】修改UBT源码，和restore二选一")
	ubtCmd.Flags().BoolP("restore", "r", false, "【可选】撤销对UBT源码的修改，和modify二选一")
	ubtCmd.Flags().BoolP("ios", "i", false, `【可选】修改iOS平台UBT部分源码,支持以下功能:
 Signin with apple、
 Notification Push、
 Universal Link、
 Swift Link Flag、
 Location Description`)
	//ubtCmd.Flags().BoolP("android", "a", false, `【可选】修改Android平台UBT部分源码,支持以下功能:GameApplication替换`)
	ubtCmd.Flags().BoolP("mac", "m", false, `【可选】修改Mac平台UBT部分源码,支持以下功能:
 添加-ObjC编译参数、
 修改高版本XCode编译报错`)
	ubtCmd.Flags().BoolP("build", "b", false, "【可选】重新编译UBT")
}
