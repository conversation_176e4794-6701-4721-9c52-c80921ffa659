package cmd

import (
	"onetools/cmd/xcode"

	"github.com/spf13/cobra"
)

var xcodeCmd = &cobra.Command{
	Use:   "xcode",
	Short: "配置xcode工程",
	Long:  "该命令用于配置xcode工程",
	Run: func(cmd *cobra.Command, args []string) {
		xcode_execute(cmd)
	},
}

func init() {
	rootCmd.AddCommand(xcodeCmd)
	xcodeCmd.Flags().StringP("projPath", "p", "", "设置`*.xcodeproj`的路径")
}

func xcode_execute(cmd *cobra.Command) {
	// 删除引擎默认生成的本地化文件
	projPath, _ := cmd.Flags().GetString("projPath")
	xcode.NewXCLocalization(projPath).Execute()
}
