package xcode

type XCLocalization struct {
	projPath string
}

func NewXCLocalization(projPath string) XCLocalization {
	return XCLocalization{projPath: projPath}
}

func (localization XCLocalization) Execute() {
	// 删除无用的本地语言
	localization.deleteDefaultLanguageSettings()
}

/*
***************************************
@brief 私有方法
***************************************
*/
func (localization XCLocalization) deleteDefaultLanguageSettings() {
	config := GetXcodeProj(localization.projPath)
	object, _ := config.RawProj.Object("objects")
	for _, v := range object {
		result := v.(map[string]interface{})
		if result["isa"] == "PBXVariantGroup" && result["name"] == "InfoPlist.strings" {
			result["children"] = []string{}
		}
	}
	SaveXcodeProj(config)
}
