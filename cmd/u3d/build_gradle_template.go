package u3d

import (
	"fmt"
	"onetools/cmd/utility"
	"path/filepath"
	"runtime"
)

type BuildGradleTemplate struct {
	projectPath string
	groupId     string
	artifactId  string
	version     string
}

func NewBuildGradleTemplate(projectPath string, groupId string, artifactId string, version string) *BuildGradleTemplate {
	return &BuildGradleTemplate{projectPath: projectPath, groupId: groupId, artifactId: artifactId, version: version}
}

func (template *BuildGradleTemplate) GenerateBuildGradleTemplateFile() {
	content := template.getGradleContent()
	gradleFilePath := filepath.Join(template.projectPath, "build.gradle")
	utility.WriteContentToFile(gradleFilePath, content)
}

func (template *BuildGradleTemplate) DeleteUselessFiles() {
	gradleFilePath := filepath.Join(template.projectPath, "build.gradle")
	if utility.IsExist(gradleFilePath) {
		utility.RemoveFile(gradleFilePath)
	}

	gradleCachePath := filepath.Join(template.projectPath, ".gradle")
	if utility.IsExist(gradleCachePath) {
		utility.RemoveDir(gradleCachePath)
	}
}

func (template *BuildGradleTemplate) ExecuteInstallCommand() {
	toolPath := filepath.Join(utility.GetExeDir(), "Gradle")
	var command string
	if runtime.GOOS == "windows" {
		command = fmt.Sprintf("\"%s\"  download --refresh-dependencies --info --warning-mode=all", filepath.Join(toolPath, "gradlew.bat"))
	} else {
		command = fmt.Sprintf("\"%s\"  download --refresh-dependencies --info --warning-mode=all", filepath.Join(toolPath, "gradlew"))
	}

	output := utility.ExecuteCommand(template.projectPath, command)
	fmt.Println(output)
}

func (template *BuildGradleTemplate) getGradleContent() string {
	content := `
apply plugin: 'java'
apply plugin: 'java-library'

repositories {
	maven {
		url "http://nexus.sys.wanmei.com/repository/maven-public/"
		allowInsecureProtocol = true
	}
}

dependencies {
	api "` + fmt.Sprintf("%s:%s:%s", template.groupId, template.artifactId, template.version) + `:SDK@zip"
}

configurations {
	customConfig.extendsFrom api
}

// 下载文件
task download(type: Copy) {
	from(project.configurations.customConfig) into '.'
		filesMatching '*.zip', { zipDetails ->
			copy {
				duplicatesStrategy = DuplicatesStrategy.WARN
				from zipTree(zipDetails.file) into (destinationDir)
			}
			zipDetails.exclude()
		}
	doNotTrackState("Copy needs to re-run every time")
}
`
	return content
}
