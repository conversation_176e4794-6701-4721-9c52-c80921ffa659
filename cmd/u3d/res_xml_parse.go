package u3d

import (
	"encoding/xml"
	"fmt"
	"io"
	"log"
	"net/http"
	"sort"
	"strconv"
	"strings"
)

type Metadata struct {
	XMLName    xml.Name   `xml:"metadata"`
	GroupId    string     `xml:"groupId"`
	ArtifactId string     `xml:"artifactId"`
	Versioning Versioning `xml:"versioning"`
}

type Versioning struct {
	Versions Versions `xml:"versions"`
}

type Versions struct {
	Version []string `xml:"version"`
}

var metadata Metadata

type ResXmlParse struct {
	url string
}

func NewResXmlParse(url string) *ResXmlParse {
	return &ResXmlParse{url: url}
}

func (xmlParse *ResXmlParse) GetNewestFileName() string {
	return xmlParse.getFileNameList()[0]
}

func (xmlParse ResXmlParse) GetFileName(version string) string {
	var name string
	for _, fileName := range xmlParse.getFileNameList() {
		if strings.HasPrefix(fileName, version) {
			name = fileName
			break
		}
	}
	return name
}

func (xmlParse *ResXmlParse) LogVersionListInfo() {
	var versionStr string
	for _, fileName := range xmlParse.getFileNameList() {
		versionStr += fmt.Sprintf("%s\n", strings.Split(fileName, "/")[0])
	}

	fmt.Println("Remote Versions:")
	fmt.Println(versionStr)
}

func (xmlParse *ResXmlParse) getFileNameList() []string {
	versions := metadata.Versioning.Versions.Version
	sort.Slice(versions, func(i, j int) bool {
		return xmlParse.compare(versions[i], versions[j])
	})

	return versions
}

func (xmlParse *ResXmlParse) Parse() {
	response, err := http.Get(xmlParse.url)
	if err != nil || response.StatusCode != 200 {
		log.Printf("【资源信息列表】请求资源列表信息失败 => %s\n", err.Error())
	}

	defer response.Body.Close()

	bodyBytes, err := io.ReadAll(response.Body)
	if err != nil {
		log.Printf("【资源信息列表】读取资源列表信息失败 => %s\n", err.Error())
	}

	var data Metadata
	err = xml.Unmarshal(bodyBytes, &data)
	if err != nil {
		log.Printf("【资源信息列表】信息反序列化失败 => %s\n", err.Error())
	}

	metadata = data
}

func (xmlParse *ResXmlParse) GetMetadata() Metadata {
	return metadata
}

func (xmlParse *ResXmlParse) compare(v1 string, v2 string) bool {
	major1, middle1, minor1, hot1 := xmlParse.splitVersion(strings.ReplaceAll(v1, "-SNAPSHOT", ""))
	major2, middle2, minor2, hot2 := xmlParse.splitVersion(strings.ReplaceAll(v2, "-SNAPSHOT", ""))

	if major1 > major2 {
		return true
	} else if major1 < major2 {
		return false
	}

	if middle1 > middle2 {
		return true
	} else if middle1 < middle2 {
		return false
	}

	if minor1 > minor2 {
		return true
	} else if minor1 < minor2 {
		return false
	}

	if hot1 > hot2 {
		return true
	} else if hot1 < hot2 {
		return false
	}

	return false
}

func (xmlParse *ResXmlParse) splitVersion(version string) (int, int, int, int) {
	slices := strings.Split(version, ".")

	major, _ := strconv.Atoi(slices[0])
	middle, _ := strconv.Atoi(slices[1])
	minor, _ := strconv.Atoi(slices[2])
	hotUpdate, _ := strconv.Atoi(slices[3])

	return major, middle, minor, hotUpdate
}
