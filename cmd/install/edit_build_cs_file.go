package install

import (
	"fmt"
	"io/fs"
	"log"
	"onetools/cmd/utility"
	"path/filepath"
	"regexp"
	"strings"
)

var buildCSFilePaths []string

func EditBuildCSFile(subGradles []string, nativeSDKPath string) {
	//获取.build.cs文件列表
	getBuildCSFilePath(subGradles)

	if len(buildCSFilePaths) == 0 {
		log.Printf("没有找到.build.cs文件")
		return
	}

	// 存放原生SDK的目录
	nativeSDKPath = filepath.Join(nativeSDKPath, utility.GetNativeSDKName())

	//分割第一个.build.cs文件的内容
	var compareComponents []string
	for _, buildCSFilePath := range buildCSFilePaths {
		prefix, core, suffix := splitBuildCSFileContent(buildCSFilePath)
		components := getMiddleContentList(core)
		//不处理内容为空的文件
		if len(components) == 0 {
			log.Printf("【%s】标签之间的内容为空\n", buildCSFilePath)
			continue
		}

		var content string
		if len(compareComponents) == 0 {
			compareComponents = components
			log.Printf("【%s】append before:\n %s\n", buildCSFilePath, core)
			core = appendCotent(components, nativeSDKPath)
			log.Printf("【%s】append after:\n %s\n", buildCSFilePath, core)
		} else {
			log.Printf("【%s】 delete before:\n %s\n", buildCSFilePath, core)
			core = appendCotent(components, nativeSDKPath)
			core = deleteRepeatContent(getMiddleContentList(core), compareComponents)
			log.Printf("【%s】 delete after:\n %s\n", buildCSFilePath, core)
			compareComponents = append(compareComponents, removeNoteContent(core)...)
		}

		content = fmt.Sprintf("%s// Framework=Start\n%s\t\t\t// Framework=End%s", prefix, core, suffix)
		utility.WriteContentToFile(buildCSFilePath, content)
	}
}

// 获取 .build.cs 文件列表
func getBuildCSFilePath(gradlePathParam []string) {
	for _, path := range gradlePathParam {
		utility.GetFilesFromDir(path, func(filePath string) {
			if strings.HasSuffix(filePath, ".Build.cs") {
				buildCSFilePaths = append(buildCSFilePaths, filePath)
			}
		})
	}
	log.Printf(".build.cs文件列表 => %v\n", buildCSFilePaths)
}

// 分割 .build.cs 文件内容
func splitBuildCSFileContent(filePath string) (prefix string, core string, suffix string) {
	content := utility.GetFileContent(filePath)
	componentsRegex := regexp.MustCompile("// Framework=Start|// Framework=End")
	components := componentsRegex.Split(content, -1)

	if len(components) != 3 {
		log.Printf("%s 文件被忽略", filePath)
		return "", "", ""
	}

	return components[0], components[1], components[2]
}

// 获取资源列表
func getNativeResources(dirPath string) []string {
	var resources []string
	regex := regexp.MustCompile(".bundle$")
	utility.GetSubContentFromDir(dirPath, func(path string, info fs.FileInfo) {
		_, name := filepath.Split(path)

		if regex.MatchString(name) {
			resources = append(resources, path)
		} else if !info.IsDir() {
			bundleRegex := regexp.MustCompile(".bundle/")
			fileRegex := regexp.MustCompile("(.plist|.txt|.md|.json|.xml)$")
			if !bundleRegex.MatchString(path) && fileRegex.MatchString(name) {
				resources = append(resources, path)
			}
		}
	})

	return resources
}

// 获取frameworks
func getNativeFrameworks(dirPath string) []string {
	var frameworks []string
	regex := regexp.MustCompile(".framework.zip$|.embeddedframework.zip$")
	utility.GetSubContentFromDir(dirPath, func(path string, info fs.FileInfo) {
		_, name := filepath.Split(path)

		if regex.MatchString(name) {
			frameworks = append(frameworks, path)
		}
	})

	return frameworks
}

// 获取.a
func getNativeLibs(dirPath string) []string {
	var libs []string
	regex := regexp.MustCompile(".a$")
	utility.GetSubContentFromDir(dirPath, func(path string, info fs.FileInfo) {
		_, name := filepath.Split(path)

		if regex.MatchString(name) {
			libs = append(libs, path)
		}
	})

	return libs
}

// 获取头文件
func getNativeHeaders(dirPath string) []string {
	var headers []string
	regex := regexp.MustCompile(".h$")
	utility.GetSubContentFromDir(dirPath, func(path string, info fs.FileInfo) {
		dirPath, name := filepath.Split(path)

		if regex.MatchString(name) {
			headers = append(headers, dirPath)
		}
	})

	return headers
}

func appendCotent(components []string, nativeSDKPath string) string {
	//获取下载的内容
	frameworkPaths := getNativeFrameworks(nativeSDKPath)
	libPaths := getNativeLibs(nativeSDKPath)
	resourcePaths := getNativeResources(nativeSDKPath)
	headerPaths := getNativeHeaders(nativeSDKPath)

	// build.cs文件中的依赖与已下载完成的资源进行对比，存在则保留引用，不存在，则注释引用
	for idx, component := range components {
		if strings.Contains(component, utility.GetNativeSDKName()) {
			if isExist(component, frameworkPaths, false) || isExist(component, libPaths, false) || isExist(component, resourcePaths, false) || isExist(component, headerPaths, true) {
				if strings.HasPrefix(component, "//") {
					components[idx] = strings.Trim(component[2:], " ")
				}
			} else {
				if !strings.HasPrefix(component, "//") {
					components[idx] = "//" + component
				}
			}
		}
	}

	//拼接字符串
	var ret string
	for _, component := range components {
		if len(component) > 0 {
			ret += fmt.Sprintf("\t\t\t%s\n", component)
		}
	}

	return ret
}

func deleteRepeatContent(components []string, compareComponents []string) string {

	if len(components) == 0 {
		log.Printf("components 为空，跳过该处理")
		return ""
	}

	for idx, component := range components {
		if strings.Contains(component, utility.GetNativeSDKName()) {
			sourceStr := utility.GetNativeSDKName() + strings.Split(component, utility.GetNativeSDKName())[1]
			for _, compareComponent := range compareComponents {
				if strings.Contains(compareComponent, utility.GetNativeSDKName()) {
					compareStr := utility.GetNativeSDKName() + strings.Split(compareComponent, utility.GetNativeSDKName())[1]
					if strings.Contains(strings.ToLower(compareStr), strings.ToLower(sourceStr)) && !strings.HasPrefix(component, "//") {
						components[idx] = "//" + component
						break
					}
				}
			}
		}
	}

	var ret string
	for _, component := range components {
		if len(component) > 0 {
			ret += fmt.Sprintf("\t\t\t%s\n", component)
		}
	}
	return ret
}

// 将 从*.build.cs文件中截取出的中间内容 转换成 列表
func getMiddleContentList(core string) []string {
	if core == "" {
		log.Printf("*.build.cs文件没有依赖framework，则无需处理")
		return nil
	}

	var validComponents []string

	//去掉特殊符号
	components := strings.Split(core, "\n")
	for _, component := range components {
		component = strings.ReplaceAll(component, "\t", "")
		component = strings.ReplaceAll(component, "\r", "")
		component = strings.Trim(component, " ")
		if component != "" {
			validComponents = append(validComponents, component)
		}
	}

	return validComponents
}

func isExist(component string, sourcePaths []string, isHandleHeader bool) bool {
	var isExist = false
	if isHandleHeader {
		for _, sourcePath := range sourcePaths {
			headerDirPath := filepath.Dir(sourcePath)
			if strings.Contains(headerDirPath, utility.GetNativeSDKName()) {
				relativePath := strings.ReplaceAll(utility.GetNativeSDKName()+strings.Split(headerDirPath, utility.GetNativeSDKName())[1], "\\", "/")
				if strings.Contains(strings.ToLower(component), strings.ToLower(relativePath+"\"));")) {
					isExist = true
					break
				}
			}
		}
	} else {
		for _, sourcePath := range sourcePaths {
			_, name := filepath.Split(sourcePath)

			if strings.Contains(component, name) {
				isExist = true
				break
			}
		}
	}
	return isExist
}

func removeNoteContent(core string) []string {
	var sources = getMiddleContentList(core)

	var components []string
	if len(sources) > 0 {
		for _, source := range sources {
			if !strings.HasPrefix(source, "//") {
				components = append(components, source)
			}
		}
	}

	return components
}
