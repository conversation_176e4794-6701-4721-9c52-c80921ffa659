package install

import (
	"bufio"
	"fmt"
	"io"
	"io/ioutil"
	"log"
	"onetools/cmd/utility"
	"os"
	"path/filepath"
	"regexp"
	"strings"
)

var XMLIgnoreBeginFlag = "<!--"
var XMLIgnoreEndFlag = "-->"
var RewriteUPLFile = false

// 当前注释key值类型
type IgnoreUPLKeyType int32

const (
	UPLIgnoreDefault IgnoreUPLKeyType = iota
	UPLIgnoreDescriptionKeys
	UPLIgnoreTransportSecurity
	UPLIgnoreQueriesScheme
)

func EditUPLFiles(plugins []string) {
	// 获取Plugins中build.cs和.xml文件
	var buildCSFiles []string
	var uplXMLFiles []string

	for _, path := range plugins {
		utility.GetFilesFromDir(path, func(filePath string) {
			if strings.HasSuffix(filePath, ".Build.cs") {
				buildCSFiles = append(buildCSFiles, filePath)
			} else if strings.HasSuffix(filePath, ".xml") {
				uplXMLFiles = append(uplXMLFiles, filePath)
			}
		})
	}
	// 读取所有build.cs中iOS upl文件，并确定upl完整路径是否包含在uplXMLFiles中
	var iOSUPLFiles []string
	for _, csFile := range buildCSFiles {
		uplFileName := uplFileFilter(csFile, "iOS")
		for _, uplFile := range uplFileName {
			buildCSDir := filepath.Dir(csFile)
			uplFilePath := filepath.Join(buildCSDir, uplFile)
			if utility.IsContain(uplXMLFiles, uplFilePath) {
				iOSUPLFiles = append(iOSUPLFiles, uplFilePath)
				fmt.Printf("iOS UPL文件 Path: %s\n", uplFilePath)
			}
		}
	}

	//读取分析UPL中重复的key，将重复的key注释
	var allInfoPlistKeys []string
	for _, uplFilePath := range iOSUPLFiles {
		RewriteUPLFile = false
		file, err := os.Open(uplFilePath)
		if err != nil {
			fmt.Printf("【%s】文件打开失败，error:%s \n", uplFilePath, err)
			return
		}
		defer file.Close()

		var fileContents string
		ignoreNextLine := UPLIgnoreDefault
		securityDicNum := 0
		br := bufio.NewReader(file)
		for {
			line, _, err := br.ReadLine()
			if err == io.EOF {
				// 读取到结尾，判断是否要重新写入文件
				if RewriteUPLFile {
					rewriteToFile(uplFilePath, fileContents)
				}
				break
			}

			newLineText := string(line)
			// 当前行是否为注释状态，注释状态直接添加到fileContents，不做处理
			if strings.Contains(string(line), XMLIgnoreBeginFlag) {
				// 追加到新的文本内容中
				fileContents += fmt.Sprintf("%s\n", newLineText)
				continue
			}

			// 不为UPLIgnoreDefault状态，需要注释掉
			if ignoreNextLine != UPLIgnoreDefault {
				// 查找到Desc key所在行后，是否需要注释掉下一行文本内容
				if ignoreNextLine == UPLIgnoreDescriptionKeys {
					if strings.Contains(string(line), "<string>") {
						// 防止下一行为空行,或为注释状态，为注释状态需要继续注释下一行
						ignoreNextLine = UPLIgnoreDefault
					}
				} else if ignoreNextLine == UPLIgnoreQueriesScheme {
					if strings.Contains(string(line), "</array>") {
						// </array>为结尾状态且不为注释状态
						ignoreNextLine = UPLIgnoreDefault
					}
				} else if ignoreNextLine == UPLIgnoreTransportSecurity {
					if strings.Contains(string(line), "</dict>") {
						securityDicNum--
						if securityDicNum == 0 {
							// </dict>为结尾状态,且要到成对出现后最后一个将ignoreNextLine状态还原
							ignoreNextLine = UPLIgnoreDefault
						}
					} else if strings.Contains(string(line), "<dict>") {
						securityDicNum++
					}
				}
				// 前后添加注释符
				newLineText = XMLIgnoreBeginFlag + string(line) + XMLIgnoreEndFlag
				// 追加到新的文本内容中
				fileContents += fmt.Sprintf("%s\n", newLineText)
			} else {
				//处理NS***DescriptionKeys
				matchKey, nextLineIgnoreType := filterDescriptionKeys(string(line), allInfoPlistKeys)
				ignoreNextLine = nextLineIgnoreType

				// 处理QueriesSchemes
				if matchKey == "" && nextLineIgnoreType == UPLIgnoreDefault {
					matchKey, nextLineIgnoreType = filterQueriesSchemesKeys(string(line), allInfoPlistKeys)
					ignoreNextLine = nextLineIgnoreType
				}

				// 处理AppTransportSecurity
				if matchKey == "" && nextLineIgnoreType == UPLIgnoreDefault {
					matchKey, nextLineIgnoreType = filterAppTransportSecurityKeys(string(line), allInfoPlistKeys)
					ignoreNextLine = nextLineIgnoreType
				}

				// 不为UPLIgnoreDefault状态，需要注释掉
				if nextLineIgnoreType != UPLIgnoreDefault {
					newLineText = XMLIgnoreBeginFlag + string(line) + XMLIgnoreEndFlag
				}

				// allInfoPlistKeys中未包含匹配的key时，追加到allInfoPlistKeys
				if matchKey != "" {
					allInfoPlistKeys = append(allInfoPlistKeys, matchKey)
				}
				// 追加到新的文本内容中
				fileContents += fmt.Sprintf("%s\n", newLineText)
			}
		}
	}
}

// upl文件过滤，获取指定平台的upl文件
func uplFileFilter(buildcsFilePath string, platform string) []string {
	var filterUPLFiles []string

	if !utility.IsExist(buildcsFilePath) {
		fmt.Printf("【%s】文件不存在\n", buildcsFilePath)
		return filterUPLFiles
	}

	lineBytes, err := ioutil.ReadFile(buildcsFilePath)
	if err != nil {
		fmt.Println(err)
		return filterUPLFiles
	}
	sourceContents := string(lineBytes)

	uplMatchText := "AdditionalPropertiesForReceipt.Add\\(.*\\);"
	matchRegexp, _ := regexp.Compile(uplMatchText)
	matchStrings := matchRegexp.FindAllString(sourceContents, -1)
	for _, resultString := range matchStrings {
		xmlMatchText := "(\")[^,]*.xml\""
		xmlMatchRegexp, _ := regexp.Compile(xmlMatchText)
		xmlMatchStrings := xmlMatchRegexp.FindString(resultString)
		xmlMatchStrings = strings.ReplaceAll(xmlMatchStrings, "\"", "")
		if platform == "iOS" && strings.Contains(resultString, "IOSPlugin") {
			filterUPLFiles = append(filterUPLFiles, xmlMatchStrings)
		} else if platform == "android" && strings.Contains(resultString, "AndroidPlugin") {
			// Android的UPL文件
			filterUPLFiles = append(filterUPLFiles, xmlMatchStrings)
		}
	}
	return filterUPLFiles
}

// NSDescriptionKeys过滤，未包含的key存到allDescKeys，已包含的需要注释掉
func filterDescriptionKeys(lineText string, allDescKeys []string) (matchDescKey string, ignoreNetxLintType IgnoreUPLKeyType) {
	// 匹配当前行是否包含Description Key
	matchDescRegexp, _ := regexp.Compile("<key>NS.*Description</key>")
	matchDescString := matchDescRegexp.FindString(lineText)
	if matchDescString != "" && !strings.Contains(lineText, XMLIgnoreBeginFlag) {
		// fmt.Println(matchDescString)
		if utility.IsContain(allDescKeys, matchDescString) {
			// 已包含当前匹配到的key，需要做注释处理
			RewriteUPLFile = true
			return "", UPLIgnoreDescriptionKeys
		} else {
			// 未包含当前匹配到的key，加到数组
			return matchDescString, UPLIgnoreDefault
		}
	}
	return "", UPLIgnoreDefault
}

// QueriesSchemes过滤，未包含的key存到allDescKeys，已包含的需要注释掉
func filterQueriesSchemesKeys(lineText string, allDescKeys []string) (matchDescKey string, ignoreNetxLintType IgnoreUPLKeyType) {
	// 匹配当前行是否包含Description Key
	matchSchemesRegexp, _ := regexp.Compile("<key>LSApplicationQueriesSchemes</key>")
	matchSchemesString := matchSchemesRegexp.FindString(lineText)
	if matchSchemesString != "" && !strings.Contains(lineText, XMLIgnoreBeginFlag) {
		// fmt.Println(matchSchemesString)
		if utility.IsContain(allDescKeys, matchSchemesString) {
			// 已包含当前匹配到的key，需要做注释处理
			RewriteUPLFile = true
			return "", UPLIgnoreQueriesScheme
		} else {
			// 未包含当前匹配到的key，加到数组
			return matchSchemesString, UPLIgnoreDefault
		}
	}
	return "", UPLIgnoreDefault
}

// QueriesSchemes过滤，未包含的key存到allDescKeys，已包含的需要注释掉
func filterAppTransportSecurityKeys(lineText string, allDescKeys []string) (matchDescKey string, ignoreNetxLintType IgnoreUPLKeyType) {
	// 匹配当前行是否包含Description Key
	matchSchemesRegexp, _ := regexp.Compile("<key>NSAppTransportSecurity</key>")
	matchSchemesString := matchSchemesRegexp.FindString(lineText)
	if matchSchemesString != "" && !strings.Contains(lineText, XMLIgnoreBeginFlag) {
		// fmt.Println(matchSchemesString)
		if utility.IsContain(allDescKeys, matchSchemesString) {
			// 已包含当前匹配到的key，需要做注释处理
			RewriteUPLFile = true
			return "", UPLIgnoreTransportSecurity
		} else {
			// 未包含当前匹配到的key，加到数组
			return matchSchemesString, UPLIgnoreDefault
		}
	}
	return "", UPLIgnoreDefault
}

// 重新写入文件
func rewriteToFile(filePath string, fileContent string) error {
	file, err := os.OpenFile(filePath, os.O_RDWR|os.O_TRUNC, 0666)
	if err != nil {
		log.Println("文件打开失败:", filePath, "\nerror:", err)
		return err
	}
	defer file.Close() // 关闭文件
	_, err = file.WriteString(fileContent)
	if err != nil {
		log.Println("文件写入失败:", filePath, "\nerror:", err)
		return err
	}

	return nil
}
