package install

import (
	"archive/zip"
	"crypto"
	"crypto/md5"
	"crypto/rand"
	"crypto/rsa"
	"crypto/x509"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"io/ioutil"
	"log"
	"net/url"
	"onetools/cmd/utility"
	"os"
	"path/filepath"
	"regexp"
	"strings"
	"time"
)

const (
	privateKey = "MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQCueK/+xxAt7CtQmKFcITJ4hThBc733Weun64xwXJ4K3pIhjK4ivwTFafq18mMF9TuWWFFAmNdqYi2X+BkehY+Qxz88o+WxM/+XyaPEnhzAr8hSSsslGftFoBjzM3H4j0KPrCtZ9j+xYoeIuGOiKozIzeG+7RocFyj2fNpNvHVRia8uh4h7aI51mPtwtMnw5ma5xCQBz27zt7x6u1vre8pxzZkTduOaXxSsbZxG1xLQn9spDlUIplhd6X1nMfvSUQDMuir69EwDcP/30bqS67izPvvqw5IPa/ZiwSXsHaMLvK+RO4ik2xgKfBp9dgbqyLWKDF08Jqd3LIVzzGQHWIBXAgMBAAECggEBAI70MDAvZuBZu/vjs4LLaJE7hqaKcUJ0mRQTehSurof1agK3RA8QOJaQiW3/VoeRMeu0HY8yLH7sRrNYAyzw/nr/5p6HJDKXinsSKm91pdVzsk1LHzwdF7cqwIa0xlOE/mK+DR8/F1z+gFLT0j8TYhs+JhPMLjKJ/3RHRI+hNPdC3uV6fbRxbgQDCYofSEQXTFp0e57t584V5THdpo1IarL/vshzv7cYNBJsyz6F6H4pVRg4bEp2jhwg6PxywGM+khI+7yWRv2r3nY/HGLzccUgH0KOQ0qP7d8GLOgVgMgQFFMrceHvTlQoWVEFFlvSOyxCnTKbfHNM+60ZM9rNBz2ECgYEA6noQGqyyqxcLew3PfFxaPe0r52PfLGk8qndP1iJlHFSrzhm+M+FLzsNHIkVhnfCefZW0LrYo2BmY3Rm8B+DOZQfKaf8XXJNkUpom4vvhUQb/wJpX4jTPytKzdF0iR2Y2uwmxlaAt49YsO/K71nksNOZfxPrStZ/ALfQ2S/uBA38CgYEAvnyRHp3Pg/ntSlGE9iYqRd87Mh82sxb/IX+/9foj+zSaxYLWxJcy0XWL0thA4HS2A/g5INNxx7BW5Q+l2g5xmgVA6FAA4Hbyopgt57ar56MgYicBB5RxYROl8BqeOZPjX5O1c0AdwhfmujTnvtX8RZo7phRwbTXshAUmEwLFjykCgYBn/d7qlbFgJAZZexSMqUD0uh2AsFCol7vqNqRX6kVBvWO9MrmitMc0WnNQBcl3jOId/Kk/FlcjEKc1WFaGf/PJ5xykyhgIXO5fnDKm9ONHVn1TZKCZmKaX477nWu77D6Ayb+gs3i+8Hp7br5oTUwaCJ7JN4JhXKRTIzMjQBil/7QKBgFne23jea7GQkOunWlvn1bnxgDQ9lckY6HYdl6utwJwcTYkyGbHWxBseokFv1ewQJG/rBMj/+YaFI/oeT9l5Rw+rutvgIwnEFOWxdrcD8EbxJ64nSM0StAthAcEuTchMhdHqFomxjlisficMHupiews7oTjSG9jqwBPpefUuMZdBAoGBAKv4879GFH8CCpAEY0nyOWOW95FybYvuptHojfFJlB2c/TCR1h23ztikuc8bleAjnXoOf3Z9Pwdn8XGDOGSG42nARQokzno0dRyqMEtTlmq+r6Gwz9uGPoS5Cb8LBF+XeA/lk7OMEGn1IkgXbuN53+sRvfIpAYCfC/0UsCN+o+bw"
)

type UProjectVersionInfo struct {
	VersionName string `json:"VersionName"`
}

// 请求获取配置文件
func FetchProjectConfig(ProjectPath string, DownloadPath string, requestParams map[string]string, platformOS utility.PlatformOS, devHostUrl string) error {
	targetPath := filepath.Join(DownloadPath, utility.GetNativeSDKName())
	configDirPath := filepath.Join(targetPath, NativeSDKConfig)

	targetPlatform := ""
	if platformOS == utility.PlatformOS_IOS {
		targetPlatform = "ios"
	} else if platformOS == utility.PlatformOS_OSX {
		targetPlatform = "macos"
	} else if platformOS == utility.PlatformOS_PS4 || platformOS == utility.PlatformOS_PS5 || platformOS == utility.PlatformOS_PS {
		targetPlatform = "ps"
	} else {
		return fmt.Errorf("其他平台【%d】不需要下载配置文件", platformOS)
	}

	hostUrl := "https://dev.sys.wanmei.net/"
	// 本地测试url
	if devHostUrl != "" {
		hostUrl = devHostUrl
	}
	hostUrl, _ = url.JoinPath(hostUrl, "api/open/packCompile/param/", targetPlatform)
	engineType := ""
	appId := ""
	env := ""
	sdkRegion := ""
	sdkVersion := ""

	// 支持除固定参数外，添加其他扩展参数
	queryParams := url.Values{}
	for key, value := range requestParams {
		if strings.EqualFold(key, "appid") {
			appId = value
		} else if strings.EqualFold(key, "env") {
			env = value
		} else if strings.EqualFold(key, "sdkregion") {
			sdkRegion = value
		} else if strings.EqualFold(key, "sdkversion") {
			sdkVersion = value
		} else {
			queryParams.Add(key, value)
		}
	}

	if utility.IsUEProject(ProjectPath) {
		engineType = "1" //1-UnrealEngine
		ueDefaultGamePath := filepath.Join(ProjectPath, "Config", "DefaultGame.ini")
		iOSConfigSectionName := "[/Script/OneEngineEditor.OneEngineSettings]"
		iniLineBytes, err := ioutil.ReadFile(ueDefaultGamePath)
		if err != nil {
			log.Printf("【%s】配置文件读取失败，可能老版本，不需要下载配置文件\n", ueDefaultGamePath)
			return err
		}
		iniContentsStr := string(iniLineBytes)

		//sdkRegion根据AppID请求服务器返回的值决定，本地值暂不不使用了，只通过AppID获取配置信息就够了
		if len(sdkRegion) == 0 {
			sdkRegion = GetIniValueInSection(iOSConfigSectionName, "SDKRegion", iniContentsStr)
			if len(sdkRegion) == 0 {
				sdkRegion = "mainland" // 默认mainland
			}
		}

		if len(appId) == 0 {
			appId = GetIniValueInSection(iOSConfigSectionName, "AppID", iniContentsStr)
		}

		if len(appId) == 0 {
			return fmt.Errorf("【%s】未获取到AppId,不需要下载配置文件\n", ProjectPath)
		}

		if len(env) == 0 {
			env = GetIniValueInSection(iOSConfigSectionName, "Env", iniContentsStr)
		}

		if len(sdkVersion) == 0 {
			// 获取合并后OneEngineSDK版本号
			uProjectPath := filepath.Join(DownloadPath, "OneEngineSDK", "OneEngineSDK.uplugin")

			if utility.IsExist(uProjectPath) {
				uProjectLineBytes, err := ioutil.ReadFile(uProjectPath)
				if err != nil {
					return fmt.Errorf("【%s】 OneEngineSDK.uplugin配置文件读取失败\n", uProjectPath)
				}
				// 创建一个 VersionInfo 结构体
				var VersionInfo UProjectVersionInfo
				err = json.Unmarshal(uProjectLineBytes, &VersionInfo)
				if err != nil {
					return fmt.Errorf("【%s】 获取OneEngineSDK.uplugin版本号失败\n", uProjectPath)
				}
				sdkVersion = VersionInfo.VersionName
			} else {
				sdkVersion = "1.0"
			}
		}
	} else if utility.IsUnityProject(ProjectPath) {
		engineType = "0" //0-Unity
		if len(appId) == 0 {
			return fmt.Errorf("【%s】未获取到AppId,不需要下载配置文件\n", ProjectPath)
		}

		if len(env) == 0 {
			return fmt.Errorf("【%s】未获取到Env,不需要下载配置文件\n", ProjectPath)
		}

	} else {
		return fmt.Errorf("暂不支持非Unity、UnrealEnagine的项目: 【%s】\n", ProjectPath)
	}

	queryParams.Add("type", engineType)
	queryParams.Add("appId", appId)
	queryParams.Add("env", env)
	queryParams.Add("sdkVersion", sdkVersion)
	timestamp := fmt.Sprintf("%d", time.Now().UnixNano()/1e6)
	queryParams.Add("t", timestamp)

	// 此处添加调整配置文件路径的逻辑，是为了保证unity能够保证多个地区正常出包
	if !utility.IsUEProject(ProjectPath) && utility.IsUnityProject(ProjectPath) {
		configDirPath = filepath.Join(configDirPath, fmt.Sprintf("%s_%s", appId, env))
	}

	// 发送获取配置信息请求
	err := SendRequestConfig(queryParams, hostUrl, targetPlatform, configDirPath, ProjectPath)
	if err != nil {
		// 配置文件网络请求失败，或下载解压失败，只接抛异常，中断后续流程
		fmt.Printf("\n")
		log.Panicf("未获取到配置文件: 【%s】 \n", err)
	}
	return nil
}

func SendRequestConfig(queryParams url.Values, hostUrl string, targetPlatform string, configDirPath string, ProjectPath string) error {
	//进行转码使之可以安全的用在URL查询里
	queryUrl, err := url.QueryUnescape(queryParams.Encode())
	if err != nil {
		// fmt.Println("QueryUnescape", err)
		return err
	}
	if sign, err := rsaSignWithMd5(queryUrl, privateKey); err != nil {
		log.Printf("获取配置文件签名错误,params:【%s】\n", queryParams.Encode())
		return err

	} else {
		queryParams.Add("sign", sign)
	}
	urlQuery := queryParams.Encode()
	downloadConfigUrl := hostUrl + "?" + urlQuery
	log.Printf("配置文件下载URL: 【%s】 \n", downloadConfigUrl)

	tempDir := utility.GetTempDir()
	// 将时间戳转换为字符串
	downloadFilenName := fmt.Sprintf("%senginesdkconfig_%d", targetPlatform, time.Now().Unix())

	zipPath, loadErr := utility.DownloadFileWithName(downloadConfigUrl, tempDir, downloadFilenName)
	if loadErr != nil {
		log.Printf("【%s】配置文件请求失败: 【%s】 \n", hostUrl, loadErr)
		return loadErr
	}
	defer os.Remove(zipPath)

	if strings.Contains(hostUrl, "dev-test.") || strings.Contains(hostUrl, "dev-staging.") {
		//开发测试，将zip拷贝到Log下一份，方便QA测试查看
		logDirPath := filepath.Join(ProjectPath, "Logs")
		if !utility.IsDir(logDirPath) {
			// 没有Logs目录,创建一个
			os.MkdirAll(logDirPath, 0777)
		}
		logZipPath := filepath.Join(logDirPath, filepath.Base(zipPath)+".zip")
		utility.FileCopy(zipPath, logZipPath)
		log.Printf("配置文件拷贝到Logs目录下存档：【%s】 \n", zipPath)
	}

	// 解压并替换文件
	isCompressedFile, _ := utility.IsCompressedFile(zipPath)
	if isCompressedFile {
		// 删除缓存
		os.RemoveAll(configDirPath)
		os.MkdirAll(configDirPath, 0777)
		fmt.Printf("Config文件目录路径:【%s】", configDirPath)

		unzipErr := unzipConfigFiles(zipPath, configDirPath)
		if unzipErr != nil {
			log.Printf("【%s】配置文件解压失败: 【%s】 \n", zipPath, unzipErr)
			return unzipErr
		}
	} else {
		responseContent, err := ioutil.ReadFile(zipPath)
		if err != nil {
			log.Printf("请检查网络配置参数 \n")
			return err
		} else {
			// 返回输出接口返回的错误
			return fmt.Errorf("%s", responseContent)
		}
	}
	return nil
}

// 签名
func rsaSignWithMd5(data string, prvKey string) (sign string, err error) {

	//如果密钥是urlSafeBase64的话需要处理下
	prvKey = Base64URLDecode(prvKey)

	keyBytes, err := base64.StdEncoding.DecodeString(prvKey)
	if err != nil {
		// fmt.Println("DecodeString:", err)
		return "", err
	}

	privateKey, err := x509.ParsePKCS8PrivateKey(keyBytes)
	if err != nil {
		// fmt.Println("ParsePKCS8PrivateKey", err)
		return "", err
	}

	h := md5.New()
	h.Write([]byte(data))
	hash := h.Sum(nil)
	signature, err := rsa.SignPKCS1v15(rand.Reader, privateKey.(*rsa.PrivateKey), crypto.MD5, hash[:])
	if err != nil {
		// fmt.Println("SignPKCS1v15:", err)
		return "", err
	}

	out := base64.RawURLEncoding.EncodeToString(signature)
	return out, nil
}

// 因为Base64转码后可能包含有+,/,=这些不安全的URL字符串，所以要进行换字符
// '+' -> '-'
// '/' -> '_'
// '=' -> ”
// 字符串长度不足4倍的位补"="
func Base64URLDecode(data string) string {
	var missing = (4 - len(data)%4) % 4
	data += strings.Repeat("=", missing) //字符串长度不足4倍的位补"="
	data = strings.Replace(data, "_", "/", -1)
	data = strings.Replace(data, "-", "+", -1)
	return data
}

// 解压配置文件到指定目录
func unzipConfigFiles(zipPath string, destDir string) error {
	// 打开 zip 文件
	r, err := zip.OpenReader(zipPath)
	if err != nil {
		return err
	}
	defer r.Close()

	// 遍历 zip 文件中的每个文件
	for _, f := range r.File {
		filename := filepath.Base(f.Name)

		// Skip hidden files
		if strings.HasPrefix(filename, ".") {
			continue
		}
		// 构造文件的完整路径
		destFileName := f.Name
		path := filepath.Join(destDir, destFileName)
		// 如果文件是一个目录，则递归地创建它
		if f.FileInfo().IsDir() {
			err := os.MkdirAll(path, f.Mode())
			if err != nil {
				return err
			}
			continue
		}

		// 打开 zip 文件中的文件
		rc, err := f.Open()
		if err != nil {
			return err
		}
		defer rc.Close()

		// 创建或截断磁盘上的文件
		w, err := os.Create(path)
		if err != nil {
			return err
		}
		defer w.Close()

		// 将文件的内容从 zip 文件中复制到磁盘上的文件
		if _, err := io.Copy(w, rc); err != nil {
			return err
		}
	}

	return nil
}

// UE解析dynamic.ini，添加到工程IOSEngine.ini和IOSGame.ini文件中
func FormattingUEIniConfig(projectPath string, downloadPath string, platformOS utility.PlatformOS) {
	iniConfigDirPath := filepath.Join(downloadPath, utility.GetNativeSDKName(), NativeSDKConfig, "dynamic.ini")
	if !utility.IsExist(iniConfigDirPath) {
		log.Printf("【%s】 不存在Ini配置文件\n", iniConfigDirPath)
		return
	}
	pluginPlatform := ""
	if platformOS == utility.PlatformOS_IOS {
		pluginPlatform = "IOS"
	} else if platformOS == utility.PlatformOS_OSX {
		pluginPlatform = "Mac"
	} else if platformOS == utility.PlatformOS_PS || platformOS == utility.PlatformOS_PS4 || platformOS == utility.PlatformOS_PS5 {
		handlePSConfigFile(projectPath, downloadPath, platformOS)
		return
	} else {
		log.Printf("不需要格式化平台【%d】的配置文件\n", platformOS)
		return
	}

	iOSUEConfigDirPath := filepath.Join(projectPath, "Config", pluginPlatform)
	if !utility.IsDir(iOSUEConfigDirPath) {
		// 没有Config/IOS目录,创建一个
		os.MkdirAll(iOSUEConfigDirPath, 0777)
	}

	//读取动态ini文件内容
	iniLineBytes, err := ioutil.ReadFile(iniConfigDirPath)
	if err != nil {
		log.Printf("【%s】 Ini配置文件读取失败\n", iniConfigDirPath)
		return
	}
	iniContentsStr := string(iniLineBytes)

	if platformOS == utility.PlatformOS_OSX {
		// 获取[/Script/OneEngineSDK]配置内容
		oneConfigSectionName := "[/Script/OneEngineSDK]"
		oneConfigMatch := GetIniSectionContent(oneConfigSectionName, iniContentsStr)
		macGameIniPath := filepath.Join(iOSUEConfigDirPath, "MacGame.ini")
		modifyErr := ModifyIniSectionContent(oneConfigSectionName, oneConfigMatch, macGameIniPath)
		if modifyErr == nil {
			//都修改成功，删除下载的ini临时文件即可
			os.Remove(iniConfigDirPath)
		}
	} else if platformOS == utility.PlatformOS_IOS {
		// 获取[/Script/IOSConfig.IOSConfigSettings]配置内容
		iOSConfigSectionName := "[/Script/IOSConfig.IOSConfigSettings]"
		iOSConfigMatch := GetIniSectionContent(iOSConfigSectionName, iniContentsStr)
		iOSEngineIniPath := filepath.Join(iOSUEConfigDirPath, "IOSEngine.ini")

		modifyErr1 := ModifyIniSectionContent(iOSConfigSectionName, iOSConfigMatch, iOSEngineIniPath)
		// 获取[/Script/OneEngineSDK]配置内容
		oneConfigSectionName := "[/Script/OneEngineSDK]"
		oneConfigMatch := GetIniSectionContent(oneConfigSectionName, iniContentsStr)
		iOSGameIniPath := filepath.Join(iOSUEConfigDirPath, "IOSGame.ini")
		modifyErr2 := ModifyIniSectionContent(oneConfigSectionName, oneConfigMatch, iOSGameIniPath)

		// 获取[/Script/OneEngineSDKPlistData]，生成UPL文件
		uplXmlFileDirPath := filepath.Join(downloadPath, utility.GetNativeSDKName(), NativeSDKAdditional)
		HandlePrivacyDescToUPLXML(iniConfigDirPath, uplXmlFileDirPath)
		if modifyErr1 == nil && modifyErr2 == nil {
			//都修改成功，删除下载的ini临时文件即可
			os.Remove(iniConfigDirPath)
		}
	}
}

// UE读取Ini文件，获取指定section内容
func GetIniSectionContent(sectionName string, iniConfigContent string) string {
	sectionEnd := `;|#|(\[.*\])` //兼容;、#的注释和常规[section]样式
	reSectionStart := regexp.MustCompile(regexp.QuoteMeta(sectionName))
	reSectionEnd := regexp.MustCompile(sectionEnd)

	startIndex := reSectionStart.FindStringIndex(iniConfigContent)
	if startIndex != nil {
		remainingText := iniConfigContent[startIndex[1]:]
		// 查找下一个[xxx]，如果没有，则表示全部为当前section了
		endIndex := reSectionEnd.FindStringIndex(remainingText)
		if endIndex == nil {
			endIndex = []int{len(remainingText), len(remainingText)}
		}
		contentMatch := remainingText[:endIndex[0]]
		return contentMatch
	}
	return ""
}

// UE读取Ini文件，获取指定section中key的值
func GetIniValueInSection(sectionName string, keyName string, iniConfigContent string) string {
	iniSectionContent := GetIniSectionContent(sectionName, iniConfigContent)
	keyPattern := fmt.Sprintf(`(?mi)%s\s*=\s*([^\r\n]+)`, keyName)
	// 编译正则表达式
	re := regexp.MustCompile(keyPattern)

	// 查找匹配项
	keyMatch := re.FindStringSubmatch(iniSectionContent)
	keyValue := ""
	if len(keyMatch) > 1 {
		keyValue = keyMatch[1]
	}
	log.Printf("读取Ini文件【%s-%s】的值为【%s】\n", sectionName, keyName, keyValue)
	return keyValue
}

// UE Ini文件插入指定内容
func ModifyIniSectionContent(sectionName string, newSectionContent string, iniFilePath string) error {
	newSectionContent = ensureDoubleNewlineAtEnd(newSectionContent)

	if !utility.IsExist(iniFilePath) {
		// ini文件不存在，直接创建写入文件内容
		iniFileContent := sectionName + newSectionContent

		// 创建文件
		file, err := os.Create(iniFilePath)
		if err != nil {
			log.Printf("【%s】创建失败，Error:%s】\n", iniFilePath, err)
			return err
		}
		defer file.Close()
		// 写入内容
		_, err = file.WriteString(iniFileContent)
		if err != nil {
			log.Printf("【%s】写入失败，Error:%s】\n", iniFilePath, err)
			return err
		} else {
			log.Printf("【%s】写入成功\n", iniFilePath)
		}
	} else {
		// 读取当前已有ini文件内容
		iniLineBytes, err := ioutil.ReadFile(iniFilePath)
		if err != nil {
			log.Printf("【%s】 Ini配置文件读取失败\n", iniFilePath)
			return err
		}
		iniContentsStr := string(iniLineBytes)
		sectionContentMatch := GetIniSectionContent(sectionName, iniContentsStr)
		newIniFileContent := ""
		if sectionContentMatch != "" {
			// 已存在目标section，对section内容进行替换
			newIniFileContent = strings.Replace(iniContentsStr, sectionContentMatch, newSectionContent, -1)

		} else {
			// 不存在目标section，在已有ini内容后追加
			newIniFileContent = iniContentsStr + "\n" + sectionName + newSectionContent
		}

		if newIniFileContent != "" {
			file, err := os.OpenFile(iniFilePath, os.O_RDWR|os.O_TRUNC, 0666)
			if err != nil {
				log.Printf("【%s】 Ini配置文件读取失败\n", iniFilePath)
				return err
			}

			defer file.Close() // 关闭文件
			_, err = file.WriteString(newIniFileContent)
			if err != nil {
				log.Printf("【%s】写入失败，Error:%s】\n", iniFilePath, err)
				return err
			} else {
				log.Printf("【%s】写入成功\n", iniFilePath)
			}
		}
	}
	return nil
}

func ensureDoubleNewlineAtEnd(text string) string {
	if !strings.HasSuffix(text, "\n\n") {
		text += "\n"
	}
	if !strings.HasSuffix(text, "\n\n") {
		text += "\n"
	}

	return text
}

// 处理下载完成的ps配置文件，移动到指定位置
func handlePSConfigFile(projectPath string, downloadPath string, platformOS utility.PlatformOS) {
	psIniName := "PSGame.ini"
	iniConfigDirPath := filepath.Join(downloadPath, utility.GetNativeSDKName())
	iniConfigPath := filepath.Join(iniConfigDirPath, NativeSDKConfig, psIniName)
	if !utility.IsExist(iniConfigPath) {
		log.Printf("【%s】 不存在Ini配置文件\n", iniConfigPath)
		return
	}

	targetConfigDirPath := filepath.Join(downloadPath, "OneEngineSDK", NativeSDKConfig)
	if !utility.IsDir(targetConfigDirPath) {
		// 没有Config，则创建一个
		os.MkdirAll(targetConfigDirPath, 0777)
	}
	// 移动配置文件到指定目录
	targetConfigPath := filepath.Join(targetConfigDirPath, psIniName)
	err := os.Rename(iniConfigPath, targetConfigPath)
	if err != nil {
		log.Printf("移动配置文件失败: %v\n", err)
	}

	log.Printf("PS配置文件存放到指定位置: 【%s】\n", targetConfigPath)

	// 删除临时目录
	err = os.RemoveAll(iniConfigDirPath)
}
