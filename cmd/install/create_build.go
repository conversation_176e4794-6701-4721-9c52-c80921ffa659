package install

import (
	"fmt"
	"log"
	"onetools/cmd/utility"
	"os"
	"path/filepath"
	"regexp"
	"runtime"
	"strings"
)

type Gradle struct {
	pluginPaths  []string
	downloadPath string
	needUpdate   bool
}

func NewGradle(pluginPaths []string, downloadPath string, needUpdate bool) Gradle {
	return Gradle{pluginPaths: pluginPaths, downloadPath: downloadPath, needUpdate: needUpdate}
}

func (gradle Gradle) Execute() {
	// 获取gradle文件列表
	gradleFilePathList := gradle.getBuildFilePathList()
	// 生成文件内容
	log.Printf("\n************* build.gradle 文件内容 start *************\n")
	content := gradle.getBuildGradleFileContent(gradleFilePathList)
	log.Println(content)
	log.Printf("\n************* build.gradle 文件内容 end *************\n\n")
	// 写文件操作
	utility.WriteContentToFile(filepath.Join(gradle.downloadPath, "build.gradle"), content)
}

func (gradle Gradle) getBuildFilePathList() []string {
	var files []string
	for _, pluginPath := range gradle.pluginPaths {
		if !utility.IsExist(pluginPath) {
			fmt.Printf("该插件路径不存在 => %s\n", pluginPath)
			continue
		}

		err := filepath.Walk(pluginPath, visit(&files))
		if err != nil {
			panic(err.Error())
		}
	}
	log.Printf("gradle文件列表：%s\n", files)
	return files
}

func visit(files *[]string) filepath.WalkFunc {

	var regex *regexp.Regexp = getRegex()

	return func(path string, info os.FileInfo, err error) error {
		if err != nil {
			panic(err.Error())
		}
		if strings.Contains(path, "Plugins") {
			var relativePath, _ = filepath.Rel(utility.GetProjectPath(), path)
			if regex.MatchString(relativePath) && strings.HasSuffix(path, ".gradle") {
				*files = append(*files, path)
			}
		} else {
			if strings.HasSuffix(path, ".gradle") {
				*files = append(*files, path)
			}
		}

		return nil
	}
}

func (gradle Gradle) getBuildGradleFileContent(gradleFilePaths []string) string {
	content := `
apply plugin: 'java'
apply plugin: 'java-library'
` + gradle.getApplyContent(gradleFilePaths) + `
repositories {
    maven {
        url "http://nexus.sys.wanmei.com/repository/maven-public/"
        allowInsecureProtocol = true
    }
}

clean {
    delete "` + gradle.getDonwloadPath() + utility.GetNativeSDKName() + `"
}

configurations {
    customConfig.extendsFrom api
}

// 下载文件
task download(type: Copy) {
	from(project.configurations.customConfig) into "` + gradle.getDonwloadPath() + `"
    filesMatching '*.zip', { zipDetails ->
        copy {
            duplicatesStrategy = DuplicatesStrategy.WARN
            from zipTree(zipDetails.file) into (destinationDir)
        }
        zipDetails.exclude()
    }
	doNotTrackState("Copy needs to re-run every time")
}
`
	return content
}

func (gradle Gradle) getApplyContent(gradleFilePaths []string) string {
	var content string
	for _, v := range gradleFilePaths {
		content += fmt.Sprintf("apply from: '%s'\n", strings.Replace(v, "\\", "/", -1))
	}
	return content
}

func (gradle Gradle) getDonwloadPath() string {
	var useDownloadPath = "."
	if gradle.downloadPath != "" {
		useDownloadPath = utility.PathRefactorSplit(gradle.downloadPath)
	}
	useDownloadPath = strings.Replace(useDownloadPath, "\\", "/", -1)
	return useDownloadPath
}

func getRegex() *regexp.Regexp {
	var regex *regexp.Regexp
	if utility.GetPlatformOS() == utility.PlatformOS_OSX {
		if runtime.GOOS == "windows" {
			regex = regexp.MustCompile(`\\Mac\\`)
		} else {
			regex = regexp.MustCompile(`/Mac/`)
		}
	} else if utility.GetPlatformOS() == utility.PlatformOS_IOS {
		if runtime.GOOS == "windows" {
			regex = regexp.MustCompile(`\\[Ii]OS\\`)
		} else {
			regex = regexp.MustCompile("/[Ii]OS/")
		}
	} else if utility.GetPlatformOS() == utility.PlatformOS_Windows {
		if runtime.GOOS == "windows" {
			regex = regexp.MustCompile(`\\Windows\\`)
		} else {
			regex = regexp.MustCompile("/Windows/")
		}
	} else if utility.GetPlatformOS() == utility.PlatformOS_PS4 {
		if runtime.GOOS == "windows" {
			regex = regexp.MustCompile(`\\PS4\\`)
		} else {
			regex = regexp.MustCompile("/PS4/")
		}
	} else if utility.GetPlatformOS() == utility.PlatformOS_PS5 {
		if runtime.GOOS == "windows" {
			regex = regexp.MustCompile(`\\PS5\\`)
		} else {
			regex = regexp.MustCompile("/PS5/")
		}
	} else if utility.GetPlatformOS() == utility.PlatformOS_PS {
		if runtime.GOOS == "windows" {
			regex = regexp.MustCompile(`\\PS\\`)
		} else {
			regex = regexp.MustCompile("/PS/")
		}
	} else if utility.GetPlatformOS() == utility.PlatformOS_Android {
		if runtime.GOOS == "windows" {
			regex = regexp.MustCompile(`\\Android\\Gradles\\`)
		} else {
			regex = regexp.MustCompile("/Android/Gradles/")
		}
	}
	return regex
}
