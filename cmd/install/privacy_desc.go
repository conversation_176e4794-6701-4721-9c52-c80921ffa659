package install

import (
	"encoding/xml"
	"io/ioutil"
	"log"
	"onetools/cmd/utility"
	"os"
	"path/filepath"
	"strings"

	"gopkg.in/ini.v1"
)

type Node struct {
	XMLName xml.Name
	Content string     `xml:",chardata"`
	Nodes   []Node     `xml:",any"`
	Attr    []xml.Attr `xml:",any,attr"`
}

// 递归地将 xml 元素解析成 Node 结构
func xmlToNodes(xmlStr string) ([]Node, error) {
	decoder := xml.NewDecoder(strings.NewReader(xmlStr))
	var nodes []Node
	var stack []*Node

	for {
		tok, err := decoder.Token()
		if err != nil {
			break
		}

		switch elem := tok.(type) {
		case xml.StartElement:
			node := &Node{
				XMLName: elem.Name,
				Attr:    elem.Attr,
			}
			if len(stack) > 0 {
				stack[len(stack)-1].Nodes = append(stack[len(stack)-1].Nodes, *node)
				stack = append(stack, &stack[len(stack)-1].Nodes[len(stack[len(stack)-1].Nodes)-1])
			} else {
				stack = append(stack, node)
			}
		case xml.CharData:
			if len(stack) > 0 {
				stack[len(stack)-1].Content = string(elem)
			}
		case xml.EndElement:
			if len(stack) > 1 {
				stack = stack[:len(stack)-1]
			}
		}
	}

	if len(stack) > 0 {
		nodes = append(nodes, *stack[0])
	}
	return nodes, nil
}

func parseValue(key, val string) []Node {
	val = strings.TrimSpace(val)

	if strings.HasPrefix(val, "<dict>") || strings.HasPrefix(val, "<array>") {
		nodes := []Node{
			{XMLName: xml.Name{Local: "key"}, Content: key},
		}
		xmlNodes, err := xmlToNodes(val)
		if err != nil {
			// fallback: treat as string
			nodes = append(nodes, Node{XMLName: xml.Name{Local: "string"}, Content: val})
		} else {
			nodes = append(nodes, xmlNodes...)
		}
		return nodes
	}

	// 默认处理为字符串
	return []Node{
		{XMLName: xml.Name{Local: "key"}, Content: key},
		{XMLName: xml.Name{Local: "string"}, Content: val},
	}
}

// 读取dynamic.ini里的[/Script/OneEngineSDKPlistData] section，将其转换成UPL XML文件，保存到Plugins/iOSNativeSDK/Additional/OneEngineAdditionalUPL.xml
func HandlePrivacyDescToUPLXML(originalIniFile string, outputFileDirPath string) {

	// 删除缓存
	if utility.IsExist(outputFileDirPath) {
		os.RemoveAll(outputFileDirPath)
	}

	if !utility.IsExist(originalIniFile) {
		log.Printf("【%s】 不存在Ini配置文件\n", originalIniFile)
		return
	}

	cfg, err := ini.Load(originalIniFile)
	if err != nil {
		log.Printf("读取 ini 文件失败:", originalIniFile, "\nerror:", err)
		return
	}

	hasSection := cfg.HasSection("/Script/OneEngineSDKPlistData")
	if hasSection == false {
		log.Println("未找到 [/Script/OneEngineSDKPlistData] section,不处理")
		return
	}

	section := cfg.Section("/Script/OneEngineSDKPlistData")
	if section == nil || len(section.KeyStrings()) == 0 {
		log.Println("[/Script/OneEngineSDKPlistData] section内容为空,不处理")
		return
	}

	root := Node{XMLName: xml.Name{Local: "root"}}
	ios := Node{XMLName: xml.Name{Local: "iosPListUpdates"}}
	add := Node{
		XMLName: xml.Name{Local: "addElements"},
		Attr: []xml.Attr{
			{Name: xml.Name{Local: "tag"}, Value: "dict"},
			{Name: xml.Name{Local: "once"}, Value: "true"},
		},
	}

	for _, key := range section.KeyStrings() {
		nodes := parseValue(key, section.Key(key).String())
		add.Nodes = append(add.Nodes, nodes...)
	}
	ios.Nodes = append(ios.Nodes, add)
	root.Nodes = append(root.Nodes, ios)

	xmlOutput, err := xml.MarshalIndent(root, "", "    ")
	if err != nil {
		log.Printf("UPL XML数据生成失败:【%s】", err)
		return
	}

	xmlOutput = append([]byte(`<?xml version="1.0" encoding="utf-8"?>`+"\n"), xmlOutput...)

	//创建目录
	os.MkdirAll(outputFileDirPath, 0777)
	outputFile := filepath.Join(outputFileDirPath, "OneEngineAdditionalUPL.xml")

	err = ioutil.WriteFile(outputFile, xmlOutput, 0644)
	if err != nil {
		log.Println("UPL XML文件写入失败:", outputFile, "\nerror:", err)
		return
	}
	log.Printf("【%s】 UPL配置文件写入成功\n", outputFile)
}

// 处理UE Localizations本地化文件，检查iOSNativeSDK/Config/Localizations目录是否存在，存在则拷贝到{UEProjectDir}/Build/IOS/Resources/Localizations/
func HandleLocalizations(projectPath string, downloadPath string, platformOS utility.PlatformOS) {

	localizationsPath := filepath.Join(downloadPath, utility.GetNativeSDKName(), NativeSDKConfig, "Localizations")
	defer os.RemoveAll(localizationsPath)

	if !utility.IsExist(localizationsPath) {
		log.Printf("【%s】目录不存在，跳过，不处理\n", localizationsPath)
		return
	}

	if platformOS != utility.PlatformOS_IOS {
		log.Printf("非iOS项目，不处理多语言本地化文本\n")
		return
	}

	targetPath := filepath.Join(projectPath, "Build", "IOS", "Resources", "Localizations")
	// 如果Localizations目录存在，表示游戏自己添加过了，跳过
	if utility.IsExist(targetPath) {
		log.Printf("【%s】多语言本地化目录已存在，跳过，不处理\n", localizationsPath)
		return
	} else {
		os.MkdirAll(targetPath, 0777)
	}

	// 拷贝文件
	utility.CopyDir(localizationsPath, targetPath)

	log.Printf("iOS本地化文件已拷贝到：%s\n", targetPath)
	//删除localizationsPath

}
