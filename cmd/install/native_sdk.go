package install

import (
	"io/fs"
	"log"
	"onetools/cmd/utility"
	"os"
	"path/filepath"
	"strings"
)

type NativeSDKHandler struct {
	sourcePath string
}

func NewNativeSDKHandler(sourcePath string) NativeSDKHandler {
	return NativeSDKHandler{sourcePath: sourcePath}
}

func (handler NativeSDKHandler) StartHandle(isOSXUEProject bool) {
	if !utility.IsExist(handler.sourcePath) {
		log.Printf("该【%s】路径不存在\n", handler.sourcePath)
		return
	}

	// 便利解压zip包
	utility.AllFilesAndDirectoriesFromPath(filepath.Join(handler.sourcePath, utility.GetNativeSDKName()), func(path string, info fs.FileInfo) {
		if strings.HasSuffix(path, ".zip") {
			destPath := filepath.Dir(path)
			// 解压SDK
			utility.NewZipHandler(path, destPath).Unzip()

			fileName := filepath.Base(path)
			fileNameWithoutExt := fileName[0 : len(fileName)-len(filepath.Ext(fileName))]
			frameworkName := fileNameWithoutExt[0:len(fileNameWithoutExt)-len(filepath.Ext(fileNameWithoutExt))] + ".framework"
			frameworkPath := filepath.Join(destPath, fileNameWithoutExt, frameworkName)
			targetPath := filepath.Join(destPath, frameworkName)

			if strings.HasSuffix(fileNameWithoutExt, ".embeddedframework") {
				if utility.IsExist(targetPath) {
					os.RemoveAll(targetPath)
				}
				os.MkdirAll(targetPath, 0777)
				// 移动文件
				moveFile(frameworkPath, targetPath)
				// 删除文件解压的目录
				os.RemoveAll(filepath.Join(destPath, fileNameWithoutExt))
			}
			// 删除zip文件
			os.RemoveAll(path)
		}
	})

	// 重命名Resources文件夹
	var oldResourcesPath = filepath.Join(handler.sourcePath, utility.GetNativeSDKName(), "Resources")
	var newResourcesPath = filepath.Join(handler.sourcePath, utility.GetNativeSDKName(), "Bundles")
	if utility.IsExist(oldResourcesPath) {
		var err = os.Rename(oldResourcesPath, newResourcesPath)
		if err != nil {
			log.Panic("重命名文件夹失败 => ", err)
		}
	}
}

func moveFile(sourcePath string, destPath string) {
	utility.AllFilesAndDirectoriesFromPath(sourcePath, func(dirPath string, d fs.FileInfo) {
		targetDirPath := strings.ReplaceAll(dirPath, sourcePath, destPath)
		if d.IsDir() {
			if utility.IsExist(targetDirPath) {
				os.RemoveAll(targetDirPath)
			}
			os.MkdirAll(targetDirPath, 0777)
		} else {
			os.Rename(dirPath, targetDirPath)
		}
	})
}
