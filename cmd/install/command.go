package install

import (
	"fmt"
	"log"
	"onetools/cmd/utility"
	"os"
	"path/filepath"
	"regexp"
	"runtime"
	"strings"
)

const NativeSDKConfig = "Config"
const NativeSDKAdditional = "Additional"

/*
1. 执行gradlew命令下载资源文件
*/

// 获取版本依赖信息
func ShowJavaEnvInfo(toolPath string) {
	var gradleCommand string
	if runtime.GOOS == "windows" {
		gradleCommand = fmt.Sprintf("\"%s\" -v", filepath.Join(toolPath, "gradlew.bat"))
	} else {
		gradleCommand = fmt.Sprintf("\"%s\" -v", filepath.Join(toolPath, "gradlew"))
	}

	out := utility.ExecuteCommand("", gradleCommand)
	// 输出日志信息
	log.Println(out)
}

// 获取版本依赖信息
func GetDependencyInfo(buildGradlePath string, toolPath string) {

	var gradleCommand string
	if runtime.GOOS == "windows" {
		gradleCommand = fmt.Sprintf("\"%s\" dependencies --configuration customConfig -Dorg.gradle.internal.http.connectionTimeout=3600000 -Dorg.gradle.internal.http.socketTimeout=3600000", filepath.Join(toolPath, "gradlew.bat"))
	} else {
		gradleCommand = fmt.Sprintf("\"%s\" dependencies --configuration customConfig -Dorg.gradle.internal.http.connectionTimeout=3600000 -Dorg.gradle.internal.http.socketTimeout=3600000", filepath.Join(toolPath, "gradlew"))
	}

	log.Printf("执行脚本命令所在目录：%s, 执行的命令：%s\n", buildGradlePath, gradleCommand)
	out := utility.ExecuteCommand(buildGradlePath, gradleCommand)
	// 输出日志信息
	log.Println(out)

	if out != "" {
		saveDependencyInfo(out, buildGradlePath)
	}
}

func RefreshDependency(buildGradlePath string, toolPath string) {
	var gradleCommand string
	if runtime.GOOS == "windows" {
		gradleCommand = fmt.Sprintf("\"%s\" download --info --warning-mode=all --refresh-dependencies -Dorg.gradle.internal.http.connectionTimeout=3600000 -Dorg.gradle.internal.http.socketTimeout=3600000", filepath.Join(toolPath, "gradlew.bat"))
	} else {
		gradleCommand = fmt.Sprintf("\"%s\" download --info --warning-mode=all --refresh-dependencies -Dorg.gradle.internal.http.connectionTimeout=3600000 -Dorg.gradle.internal.http.socketTimeout=3600000", filepath.Join(toolPath, "gradlew"))
	}

	log.Printf("执行脚本命令所在目录：%s, 执行的命令：%s\n", buildGradlePath, gradleCommand)
	output := utility.ExecuteCommand(buildGradlePath, gradleCommand)
	log.Println(output)
}

func GetGradlewPath(toolPath string) string {
	if runtime.GOOS == "windows" {
		return fmt.Sprintf("\"%s\" download --info --warning-mode=all -Dorg.gradle.internal.http.connectionTimeout=3600000 -Dorg.gradle.internal.http.socketTimeout=3600000", filepath.Join(toolPath, "gradlew.bat"))
	} else {
		return fmt.Sprintf("\"%s\" download --info --warning-mode=all -Dorg.gradle.internal.http.connectionTimeout=3600000 -Dorg.gradle.internal.http.socketTimeout=3600000", filepath.Join(toolPath, "gradlew"))
	}
}

func RunGradlew(buildGradlePath string, toolPath string) {
	// 删除缓存文件
	deleteDownloadSDKCacheDir(buildGradlePath)

	gradlewCommand := GetGradlewPath(toolPath)
	log.Println("执行脚本为：" + gradlewCommand)

	output := utility.ExecuteCommand(strings.Replace(buildGradlePath, "build.gradle", "", -1), gradlewCommand)
	log.Println(output)
}

func saveDependencyInfo(content string, gradleDirPath string) {
	contentList := strings.Split(content, "\n")

	if contentList == nil {
		log.Printf("没有依赖信息，跳过此步骤\n")
		return
	}

	regex := regexp.MustCompile(`--- `)

	var dependencyInfo string
	for _, con := range contentList {
		if regex.MatchString(con) {
			dependencyInfo += fmt.Sprintf("%s\n", con)
		}
	}

	saveFilePath := filepath.Join(gradleDirPath, "ios_sdk_dependency.txt")
	// 删除上一次生成的依赖信息
	if utility.IsExist(saveFilePath) {
		os.RemoveAll(saveFilePath)
	}

	utility.WriteContentToFile(saveFilePath, dependencyInfo)
}

func IsEmpty(path string) bool {
	dir, _ := os.ReadDir(path)
	return len(dir) == 0
}

// 删除iOSNativeSDK/目录下除Config外的目录
func deleteDownloadSDKCacheDir(path string) error {
	nativeSDKPath := filepath.Join(path, utility.GetNativeSDKName())

	info, err := os.Stat(nativeSDKPath)
	if err != nil {
		return err
	}

	if !info.IsDir() {
		return fmt.Errorf("%s is not a directory", nativeSDKPath)
	}

	files, err := os.ReadDir(nativeSDKPath)
	if err != nil {
		return err
	}

	for _, file := range files {
		subPath := filepath.Join(nativeSDKPath, file.Name())
		if file.IsDir() && file.Name() != NativeSDKConfig && file.Name() != NativeSDKAdditional {
			// fmt.Printf("Deleting directory: %s\n", subPath)
			os.RemoveAll(subPath)
		}
	}

	return nil
}
