#!/bin/bash

# 测试 --add-files-path 功能的脚本
echo "开始测试 --add-files-path 功能..."

# 设置测试参数
IPA_PATH="/Users/<USER>/Downloads/CloudHotta_v5.2.0(*******)_ra9988f2fc3_appstore.ipa"
BUNDLE_ID="com.laohunew.demosdk"
PROVISION_PATH="/Users/<USER>/Desktop/65K25Y3ZV4/comlaohunewdemosdk.mobileprovision"
CERT_NAME="Apple Development: yanshan liu (25UN2ZP5WW)"
DISPLAY_NAME="老虎重签测试"
APP_VERSION="2.0.0"
BUILD_VERSION="200"
OUTPUT_PATH="/Users/<USER>/Downloads/output_with_added_files.ipa"
ADD_FILES_PATH="$(pwd)/test_add_files_demo"

# 检查输入文件是否存在
if [ ! -f "$IPA_PATH" ]; then
    echo "错误: IPA文件不存在: $IPA_PATH"
    echo "请修改脚本中的IPA_PATH变量指向一个有效的IPA文件"
    exit 1
fi

if [ ! -f "$PROVISION_PATH" ]; then
    echo "错误: mobileprovision文件不存在: $PROVISION_PATH"
    echo "请修改脚本中的PROVISION_PATH变量指向一个有效的mobileprovision文件"
    exit 1
fi

if [ ! -d "$ADD_FILES_PATH" ]; then
    echo "错误: 要添加的文件夹不存在: $ADD_FILES_PATH"
    exit 1
fi

# 显示要添加的文件结构
echo "要添加的文件结构:"
find "$ADD_FILES_PATH" -type f | sed "s|$ADD_FILES_PATH/||"

# 删除之前的输出文件
if [ -f "$OUTPUT_PATH" ]; then
    rm "$OUTPUT_PATH"
    echo "删除之前的输出文件: $OUTPUT_PATH"
fi

# 执行重签名命令，包含新的 --add-files-path 参数
echo "执行重签名命令（包含 --add-files-path 参数）..."

./onetools ipa resign \
    -p "$IPA_PATH" \
    --bundle-id "$BUNDLE_ID" \
    --provision "$PROVISION_PATH" \
    --cert "$CERT_NAME" \
    --display-name "$DISPLAY_NAME" \
    --app-version "$APP_VERSION" \
    --build-version "$BUILD_VERSION" \
    --add-files-path "$ADD_FILES_PATH" \
    --keep-temp-dir \
    -o "$OUTPUT_PATH"

# 检查结果
if [ $? -eq 0 ]; then
    echo "重签名成功完成!"
    if [ -f "$OUTPUT_PATH" ]; then
        echo "输出文件已生成: $OUTPUT_PATH"
        echo "文件大小: $(ls -lh "$OUTPUT_PATH" | awk '{print $5}')"
    else
        echo "警告: 输出文件未找到"
    fi
else
    echo "重签名失败!"
    exit 1
fi

echo "测试完成!"
