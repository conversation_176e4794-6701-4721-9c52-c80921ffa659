{"FileVersion": 3, "EngineAssociation": "5.5", "Category": "", "PreferredSourceCodeEditor": "Rider", "Description": "", "Modules": [{"Name": "OneSDKDemo", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>", "AdditionalDependencies": ["UMG", "Slate", "SlateCore", "Engine"]}, {"Name": "GlobalSDKUE4", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>", "AdditionalDependencies": ["UMG", "Slate", "SlateCore", "Engine"]}], "Plugins": [{"Name": "EnvironmentQueryEditor", "Enabled": false}, {"Name": "AISupport", "Enabled": false}, {"Name": "ActorSequence", "Enabled": false}, {"Name": "ActorLayerUtilities", "Enabled": false}, {"Name": "AlembicImporter", "Enabled": false}, {"Name": "AndroidMedia", "Enabled": false}, {"Name": "AndroidDeviceProfileSelector", "Enabled": false}, {"Name": "AndroidMoviePlayer", "Enabled": false}, {"Name": "OnlineSubsystemGooglePlay", "Enabled": false, "SupportedTargetPlatforms": ["Android"]}, {"Name": "AnimationSharing", "Enabled": false}, {"Name": "AppleImageUtils", "Enabled": false}, {"Name": "AppleMoviePlayer", "Enabled": false}, {"Name": "ArchVisCharacter", "Enabled": false}, {"Name": "MaterialAnalyzer", "Enabled": false}, {"Name": "AssetManagerEditor", "Enabled": false}, {"Name": "AssetTags", "Enabled": false}, {"Name": "AudioCapture", "Enabled": false}, {"Name": "Synthesis", "Enabled": false}, {"Name": "AutomationUtils", "Enabled": false}, {"Name": "AvfMedia", "Enabled": false}, {"Name": "BackChannel", "Enabled": false}, {"Name": "CableComponent", "Enabled": false}, {"Name": "ChaosClothEditor", "Enabled": false}, {"Name": "ChaosCloth", "Enabled": false}, {"Name": "CameraShakePreviewer", "Enabled": false}, {"Name": "ChaosNiagara", "Enabled": false}, {"Name": "ChaosEditor", "Enabled": false}, {"Name": "ChaosSolverPlugin", "Enabled": false}, {"Name": "CharacterAI", "Enabled": false}, {"Name": "ChunkDownloader", "Enabled": false}, {"Name": "CLionSourceCodeAccess", "Enabled": false}, {"Name": "CodeLiteSourceCodeAccess", "Enabled": false}, {"Name": "CryptoKeys", "Enabled": false}, {"Name": "CurveEditorTools", "Enabled": false}, {"Name": "CustomMeshComponent", "Enabled": false}, {"Name": "PostSplashScreen", "Enabled": false, "SupportedTargetPlatforms": ["XboxOne"]}, {"Name": "DataValidation", "Enabled": false}, {"Name": "DatasmithContent", "Enabled": false}, {"Name": "Editable<PERSON><PERSON>", "Enabled": false}, {"Name": "ExampleDeviceProfileSelector", "Enabled": false}, {"Name": "FacialAnimation", "Enabled": false}, {"Name": "GameplayTagsEditor", "Enabled": false}, {"Name": "GeometryCache", "Enabled": false}, {"Name": "MeshPainting", "Enabled": false}, {"Name": "GeometryMode", "Enabled": false}, {"Name": "GitSourceControl", "Enabled": false}, {"Name": "GoogleCloudMessaging", "Enabled": false}, {"Name": "GooglePAD", "Enabled": false}, {"Name": "MediaCompositing", "Enabled": false}, {"Name": "ImgMedia", "Enabled": false}, {"Name": "IOSDeviceProfileSelector", "Enabled": false}, {"Name": "KDevelopSourceCodeAccess", "Enabled": false}, {"Name": "LauncherChunkInstaller", "Enabled": false}, {"Name": "MatineeToLevelSequence", "Enabled": false}, {"Name": "LevelSequenceEditor", "Enabled": false}, {"Name": "LightPropagationVolume", "Enabled": false}, {"Name": "NullSourceCodeAccess", "Enabled": false}, {"Name": "LinuxDeviceProfileSelector", "Enabled": false}, {"Name": "MacGraphicsSwitching", "Enabled": false}, {"Name": "MagicLeapMedia", "Enabled": false, "SupportedTargetPlatforms": ["<PERSON><PERSON>"]}, {"Name": "MagicLeap", "Enabled": false, "SupportedTargetPlatforms": ["<PERSON><PERSON>", "<PERSON>", "Win64"]}, {"Name": "MagicLeapPassableWorld", "Enabled": false, "SupportedTargetPlatforms": ["<PERSON><PERSON>", "<PERSON>", "Win64"]}, {"Name": "LuminPlatformFeatures", "Enabled": false, "SupportedTargetPlatforms": ["<PERSON><PERSON>"]}, {"Name": "MediaPlayerEditor", "Enabled": false}, {"Name": "MLSDK", "Enabled": false}, {"Name": "LocationServicesBPLibrary", "Enabled": false}, {"Name": "MobilePatchingUtils", "Enabled": false}, {"Name": "MotoSynth", "Enabled": false}, {"Name": "WebMMoviePlayer", "Enabled": false}, {"Name": "OculusVR", "Enabled": false, "SupportedTargetPlatforms": ["Win32", "Win64", "Android"]}, {"Name": "OnlineSubsystemNull", "Enabled": false}, {"Name": "OnlineSubsystemUtils", "Enabled": false}, {"Name": "OnlineSubsystemIOS", "Enabled": false, "SupportedTargetPlatforms": ["IOS", "TVOS"]}, {"Name": "OnlineSubsystem", "Enabled": false}, {"Name": "Paper2D", "Enabled": false}, {"Name": "PerforceSourceControl", "Enabled": false}, {"Name": "PhysXVehicles", "Enabled": false}, {"Name": "PlasticSourceControl", "Enabled": false}, {"Name": "PlatformCrypto", "Enabled": false}, {"Name": "SteamVR", "Enabled": false, "SupportedTargetPlatforms": ["Win32", "Win64", "Linux"]}, {"Name": "ProceduralMeshComponent", "Enabled": false}, {"Name": "ProxyLODPlugin", "Enabled": false}, {"Name": "RiderSourceCodeAccess", "Enabled": false}, {"Name": "RuntimePhysXCooking", "Enabled": false}, {"Name": "SkeletalReduction", "Enabled": false}, {"Name": "SoundFields", "Enabled": false}, {"Name": "SpeedTreeImporter", "Enabled": false}, {"Name": "SubversionSourceControl", "Enabled": false}, {"Name": "TcpMessaging", "Enabled": false}, {"Name": "UdpMessaging", "Enabled": false}, {"Name": "VariantManager<PERSON><PERSON>nt", "Enabled": false}, {"Name": "WindowsMoviePlayer", "Enabled": false}, {"Name": "MobileLauncherProfileWizard", "Enabled": false}, {"Name": "WmfMedia", "Enabled": false}, {"Name": "XGEController", "Enabled": false}], "TargetPlatforms": ["Android", "IOS", "MacNoEditor", "WindowsNoEditor", "WindowsNoEditorWin32", "LinuxAArch64NoEditor", "LinuxNoEditor"]}