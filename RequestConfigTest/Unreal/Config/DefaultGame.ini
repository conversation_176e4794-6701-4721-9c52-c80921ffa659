[ProjectSettings]
ProjectID=(A=1823396784,B=1298598689,C=1743498150,D=-2048051708)
ProjectName=First Person Template

[/Script/EngineSettings.GeneralProjectSettings]
ProjectID=6F24E3EA477A02D21EFFF8A41DBF039A
bUseBorderlessWindow=False

[/Script/UnrealEd.ProjectPackagingSettings]
Build=IfProjectHasCode
BuildConfiguration=PPBC_Shipping
BuildTarget=
StagingDirectory=(Path="C:/Users/<USER>/Downloads")
FullRebuild=False
ForDistribution=True
IncludeDebugFiles=False
BlueprintNativizationMethod=Disabled
bIncludeNativizedAssetsInProjectGeneration=False
bExcludeMonolithicEngineHeadersInNativizedCode=False
UsePakFile=True
bUseIoStore=False
bMakeBinaryConfig=False
bGenerateChunks=False
bGenerateNoChunks=False
bChunkHardReferencesOnly=False
bForceOneChunkPerFile=False
MaxChunkSize=0
bBuildHttpChunkInstallData=False
HttpChunkInstallDataDirectory=(Path="")
bCompressed=False
PakFileCompressionFormats=
bForceUseProjectCompressionFormatIgnoreHardwareOverride=False
PakFileAdditionalCompressionOptions=
PakFileCompressionMethod=Kraken
PakFileCompressionLevel_DebugDevelopment=3
PakFileCompressionLevel_TestShipping=5
PakFileCompressionLevel_Distribution=7
HttpChunkInstallDataVersion=
IncludePrerequisites=True
IncludeAppLocalPrerequisites=False
bShareMaterialShaderCode=True
bDeterministicShaderCodeOrder=False
bSharedMaterialNativeLibraries=True
ApplocalPrerequisitesDirectory=(Path="")
IncludeCrashReporter=False
InternationalizationPreset=English
-CulturesToStage=en
+CulturesToStage=en
LocalizationTargetCatchAllChunkId=0
bCookAll=False
bCookMapsOnly=False
bSkipEditorContent=False
bSkipMovies=False
-IniKeyDenylist=KeyStorePassword
-IniKeyDenylist=KeyPassword
-IniKeyDenylist=rsa.privateexp
-IniKeyDenylist=rsa.modulus
-IniKeyDenylist=rsa.publicexp
-IniKeyDenylist=aes.key
-IniKeyDenylist=SigningPublicExponent
-IniKeyDenylist=SigningModulus
-IniKeyDenylist=SigningPrivateExponent
-IniKeyDenylist=EncryptionKey
-IniKeyDenylist=IniKeyBlacklist
-IniKeyDenylist=IniSectionBlacklist
+IniKeyDenylist=KeyStorePassword
+IniKeyDenylist=KeyPassword
+IniKeyDenylist=rsa.privateexp
+IniKeyDenylist=rsa.modulus
+IniKeyDenylist=rsa.publicexp
+IniKeyDenylist=aes.key
+IniKeyDenylist=SigningPublicExponent
+IniKeyDenylist=SigningModulus
+IniKeyDenylist=SigningPrivateExponent
+IniKeyDenylist=EncryptionKey
+IniKeyDenylist=IniKeyBlacklist
+IniKeyDenylist=IniSectionBlacklist
+DirectoriesToAlwaysStageAsNonUFS=(Path="OneSDKConfig")

[/Script/PXCrash.PXCrashSettings]
AreaId=1
AndroidAppID=1161007
AndroidAppKey=0ad3a46531574de8fa412ae13889cefd
IOSAppID=1161007
IOSAppKey=0ad3a46531574de8fa412ae13889cefd

[/Script/OneEngineEditor.OneEngineSettings]
AppID=1001
SDKRegion=Mainland
Env=develop
OneAppKey=d44aad69bb50d9bb321fa1298c1cdeed
PSMainlandConfigData=
PSOverseaConfigData=
HarmonyOSConfigFilePath=E:\UE-HarmonyOS\OneSDKUE4_Union\HarmonyOS_laohu_v1.0.0_1001.config

