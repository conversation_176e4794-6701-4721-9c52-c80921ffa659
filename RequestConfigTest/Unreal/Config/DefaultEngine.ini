[/Script/Engine.CollisionProfile]
+Profiles=(Name="Projectile",CollisionEnabled=QueryOnly,ObjectTypeName="Projectile",CustomResponses=,HelpMessage="Preset for projectiles",bCanModify=True)
+DefaultChannelResponses=(Channel=ECC_GameTraceChannel1,Name="Projectile",DefaultResponse=ECR_Block,bTraceType=False,bStaticObject=False)
+EditProfiles=(Name="Trigger",CustomResponses=((Channel=Projectile, Response=ECR_Ignore)))

[/Script/EngineSettings.GameMapsSettings]
EditorStartupMap=/Game/OneSDKDemo.OneSDKDemo
LocalMapOptions=
TransitionMap=None
bUseSplitscreen=False
TwoPlayerSplitscreenLayout=Horizontal
ThreePlayerSplitscreenLayout=FavorTop
FourPlayerSplitscreenLayout=Grid
bOffsetPlayerGamepadIds=False
GameInstanceClass=/Script/OneSDKDemo.OneSDKDemoGameInstance
GameDefaultMap=/Game/OneSDKDemo.OneSDKDemo
ServerDefaultMap=/Engine/Maps/Entry.Entry
GlobalDefaultGameMode=/Game/OneSDKDemoGameModeBase.OneSDKDemoGameModeBase_C
GlobalDefaultServerGameMode=None

[/Script/IOSRuntimeSettings.IOSRuntimeSettings]
MinimumiOSVersion=IOS_11
MobileProvision=comlaohudemosdk-5(1).mobileprovision
SigningCertificate=Apple Development: Jiang Lin (GYYUNXMW52)
BundleIdentifier=com.pwrd.globalsdk.demo.new
FrameRateLock=PUFRL_60
bUseIntegratedKeyboard=True
bGeneratedSYMBundle=False
bGeneratedSYMFile=True
bGenerateCrashReportSymbols=False
bShipForBitcode=False
BundleDisplayName=GlobalUEDemo

[/Script/HardwareTargeting.HardwareTargetingSettings]
TargetedHardwareClass=Mobile
AppliedTargetedHardwareClass=Mobile
DefaultGraphicsPerformance=Maximum
AppliedDefaultGraphicsPerformance=Maximum

[/Script/Engine.Engine]

[/Script/Engine.RendererSettings]
r.Mobile.DisableVertexFog=True
r.Shadow.CSM.MaxMobileCascades=2
r.MobileMSAA=1
r.Mobile.UseLegacyShadingModel=False
r.Mobile.AllowDitheredLODTransition=False
r.Mobile.AllowSoftwareOcclusion=False
r.Mobile.VirtualTextures=False
r.DiscardUnusedQuality=False
r.AllowOcclusionQueries=True
r.MinScreenRadiusForLights=0.030000
r.MinScreenRadiusForDepthPrepass=0.030000
r.MinScreenRadiusForCSMDepth=0.010000
r.PrecomputedVisibilityWarning=False
r.TextureStreaming=True
Compat.UseDXT5NormalMaps=False
r.VirtualTextures=False
r.VirtualTexturedLightmaps=False
r.VT.TileSize=128
r.VT.TileBorderSize=4
r.vt.FeedbackFactor=16
r.VT.EnableCompressZlib=True
r.VT.EnableCompressCrunch=False
r.ClearCoatNormal=False
r.AnisotropicBRDF=False
r.ReflectionCaptureResolution=128
r.ReflectionEnvironmentLightmapMixBasedOnRoughness=True
r.ForwardShading=False
r.VertexFoggingForOpaque=True
r.AllowStaticLighting=True
r.NormalMapsForStaticLighting=False
r.GenerateMeshDistanceFields=False
r.DistanceFieldBuild.EightBit=False
r.GenerateLandscapeGIData=False
r.DistanceFieldBuild.Compress=False
r.TessellationAdaptivePixelsPerTriangle=48.000000
r.SeparateTranslucency=False
r.TranslucentSortPolicy=0
TranslucentSortAxis=(X=0.000000,Y=-1.000000,Z=0.000000)
r.CustomDepth=1
r.CustomDepthTemporalAAJitter=True
r.PostProcessing.PropagateAlpha=0
r.DefaultFeature.Bloom=True
r.DefaultFeature.AmbientOcclusion=False
r.DefaultFeature.AmbientOcclusionStaticFraction=True
r.DefaultFeature.AutoExposure=False
r.DefaultFeature.AutoExposure.Method=0
r.DefaultFeature.AutoExposure.Bias=1.000000
r.DefaultFeature.AutoExposure.ExtendDefaultLuminanceRange=False
r.UsePreExposure=True
r.EyeAdaptation.EditorOnly=False
r.DefaultFeature.MotionBlur=False
r.DefaultFeature.LensFlare=False
r.TemporalAA.Upsampling=False
r.SSGI.Enable=False
r.DefaultFeature.AntiAliasing=0
r.DefaultFeature.LightUnits=1
r.DefaultBackBufferPixelFormat=4
r.Shadow.UnbuiltPreviewInGame=True
r.StencilForLODDither=False
r.EarlyZPass=3
r.EarlyZPassOnlyMaterialMasking=False
r.DBuffer=True
r.ClearSceneMethod=1
r.BasePassOutputsVelocity=False
r.VertexDeformationOutputsVelocity=False
r.SelectiveBasePassOutputs=False
bDefaultParticleCutouts=False
fx.GPUSimulationTextureSizeX=1024
fx.GPUSimulationTextureSizeY=1024
r.AllowGlobalClipPlane=False
r.GBufferFormat=1
r.MorphTarget.Mode=True
r.GPUCrashDebugging=False
vr.InstancedStereo=False
r.MobileHDR=True
vr.MobileMultiView=False
r.Mobile.UseHWsRGBEncoding=False
vr.RoundRobinOcclusion=False
vr.ODSCapture=False
r.MeshStreaming=False
r.WireframeCullThreshold=5.000000
r.RayTracing=False
r.RayTracing.UseTextureLod=False
r.SupportStationarySkylight=True
r.SupportLowQualityLightmaps=True
r.SupportPointLightWholeSceneShadows=True
r.SupportAtmosphericFog=True
r.SupportSkyAtmosphere=True
r.SupportSkyAtmosphereAffectsHeightFog=False
r.SkinCache.CompileShaders=False
r.SkinCache.DefaultBehavior=1
r.SkinCache.SceneMemoryLimitInMB=128.000000
r.Mobile.EnableStaticAndCSMShadowReceivers=True
r.Mobile.EnableMovableLightCSMShaderCulling=True
r.Mobile.AllowDistanceFieldShadows=True
r.Mobile.AllowMovableDirectionalLights=True
r.MobileNumDynamicPointLights=4
r.MobileDynamicPointLightsUseStaticBranch=True
r.Mobile.EnableMovableSpotlights=False
r.GPUSkin.Support16BitBoneIndex=False
r.GPUSkin.Limit2BoneInfluences=False
r.SupportDepthOnlyIndexBuffers=True
r.SupportReversedIndexBuffers=True
r.SupportMaterialLayers=False
r.LightPropagationVolume=False

[/Script/Slate.SlateSettings]
bExplicitCanvasChildZOrder=True

[/Script/UnrealEd.CookerSettings]
bCookOnTheFlyForLaunchOn=False

[/Script/AndroidRuntimeSettings.AndroidRuntimeSettings]
PackageName=com.pwrd.onesdk.demo
StoreVersion=1
StoreVersionOffsetArmV7=0
StoreVersionOffsetArm64=0
StoreVersionOffsetX8664=0
ApplicationDisplayName=OneSDKUE4
VersionDisplayName=1.0
MinSDKVersion=26
TargetSDKVersion=30
InstallLocation=InternalOnly
bEnableGradle=True
bEnableLint=False
bPackageDataInsideApk=True
bCreateAllPlatformsInstall=False
bDisableVerifyOBBOnStartUp=False
bForceSmallOBBFiles=False
bAllowLargeOBBFiles=False
bAllowPatchOBBFile=False
bAllowOverflowOBBFiles=False
bUseExternalFilesDir=True
bPublicLogFiles=True
Orientation=SensorLandscape
MaxAspectRatio=2.100000
bUseDisplayCutout=False
bRestoreNotificationsOnReboot=False
bFullScreen=True
bEnableNewKeyboard=True
DepthBufferPreference=Default
bValidateTextureFormats=True
bEnableBundle=False
bEnableUniversalAPK=True
bBundleABISplit=True
bBundleLanguageSplit=True
bBundleDensitySplit=True
ExtraApplicationSettings=
ExtraActivitySettings=
bAndroidVoiceEnabled=False
bRemoveOSIG=False
+GoogleVRCaps=Daydream33
bGoogleVRSustainedPerformance=False
KeyStore=pwrd.jks
KeyAlias=pwrd
KeyStorePassword=pwrd2019
KeyPassword=
bBuildForArmV7=False
bBuildForArm64=True
bBuildForX8664=False
bBuildForES31=True
bSupportsVulkan=False
bSupportsVulkanSM5=False
bAndroidOpenGLSupportsBackbufferSampling=False
bDetectVulkanByDefault=True
bBuildWithHiddenSymbolVisibility=False
bSaveSymbols=False
bForceLDLinker=False
bEnableGooglePlaySupport=False
bUseGetAccounts=False
GamesAppID=
bEnableSnapshots=False
bSupportAdMob=True
AdMobAdUnitID=
GooglePlayLicenseKey=
GCMClientSenderID=
bShowLaunchImage=True
bAllowIMU=True
bAllowControllers=True
bBlockAndroidKeysOnControllers=False
bControllersBlockDeviceFeedback=False
AndroidAudio=Default
AudioSampleRate=44100
AudioCallbackBufferFrameSize=1024
AudioNumBuffersToEnqueue=4
AudioMaxChannels=0
AudioNumSourceWorkers=0
SpatializationPlugin=
ReverbPlugin=
OcclusionPlugin=
CompressionOverrides=(bOverrideCompressionTimes=False,DurationThreshold=5.000000,MaxNumRandomBranches=0,SoundCueQualityIndex=0)
bUseAudioStreamCaching=False
CacheSizeKB=0
MaxChunkSizeOverrideKB=0
bResampleForDevice=False
SoundCueCookQualityIndex=-1
MaxSampleRate=0.000000
HighSampleRate=0.000000
MedSampleRate=0.000000
LowSampleRate=0.000000
MinSampleRate=0.000000
CompressionQualityModifier=0.000000
AutoStreamingThreshold=0.000000
AndroidGraphicsDebugger=None
MaliGraphicsDebuggerPath=(Path="")
bMultiTargetFormat_ETC2=True
bMultiTargetFormat_DXT=True
bMultiTargetFormat_ASTC=True
TextureFormatPriority_ETC2=0.200000
TextureFormatPriority_DXT=0.600000
TextureFormatPriority_ASTC=0.900000
SDKAPILevelOverride=
NDKAPILevelOverride=
bStreamLandscapeMeshLODs=False

[/Script/IOSConfig.IOSConfigSettings]
bEnableAssociatedDomains=True
+AssociatedDomains=applinks:safestatic.games.laohu.com
bEnableSignInwithApple=True
bEnablePushNotifications=True
bEnableRemoteNotifications=True

[/Script/AndroidFileServerEditor.AndroidFileServerRuntimeSettings]
bEnablePlugin=False
bAllowNetworkConnection=True
SecurityToken=11BE8C9F4CE2EF4FCE8CA7BB0832A918
bIncludeInShipping=False
bAllowExternalStartInShipping=False
bCompileAFSProject=False
bUseCompression=False
bLogFiles=False
bReportStats=False
ConnectionType=USBOnly
bUseManualIPAddress=False
ManualIPAddress=

[/Script/MacTargetPlatform.XcodeProjectSettings]
CodeSigningTeam=65K25Y3ZV4
BundleIdentifier=com.pwrd.globalsdk.demo.new

[/Script/OpenHarmonyRuntimeSettings.OpenHarmonyRuntimeSettings]
AppName=OneGlobal
PackageName=com.wpsdk.onesdk.hm
bBuildForX8664=False
CertPath=D:\UE_HarmonyOS\keystore\onesdk_debug.cer
StorePassword=Pwrd1234
KeyAlias=pwrd
KeyPassword=Pwrd1234
Profile=D:\UE_HarmonyOS\keystore\onesdk_debug.p7b
SignAlg=SHA256withECDSA
StoreFile=D:\UE_HarmonyOS\keystore\onesdk_debug.p12
ClientID=109277209
APPID=1254406885440775680
Orientation=AUTO_ROTATION_LANDSCAPE

