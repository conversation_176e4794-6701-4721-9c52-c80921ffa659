﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <LangVersion>latest</LangVersion>
    <_TargetFrameworkDirectories>non_empty_path_generated_by_unity.rider.package</_TargetFrameworkDirectories>
    <_FullFrameworkReferenceAssemblyPaths>non_empty_path_generated_by_unity.rider.package</_FullFrameworkReferenceAssemblyPaths>
    <DisableHandlePackageFileConflicts>true</DisableHandlePackageFileConflicts>
  </PropertyGroup>
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>10.0.20506</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <RootNamespace></RootNamespace>
    <ProjectGuid>{b48de15e-cc32-5d7d-0217-95772d51420c}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <AssemblyName>Assembly-CSharp</AssemblyName>
    <TargetFrameworkVersion>v4.7.1</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <BaseDirectory>.</BaseDirectory>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>Temp\bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE;UNITY_2019_4_26;UNITY_2019_4;UNITY_2019;UNITY_5_3_OR_NEWER;UNITY_5_4_OR_NEWER;UNITY_5_5_OR_NEWER;UNITY_5_6_OR_NEWER;UNITY_2017_1_OR_NEWER;UNITY_2017_2_OR_NEWER;UNITY_2017_3_OR_NEWER;UNITY_2017_4_OR_NEWER;UNITY_2018_1_OR_NEWER;UNITY_2018_2_OR_NEWER;UNITY_2018_3_OR_NEWER;UNITY_2018_4_OR_NEWER;UNITY_2019_1_OR_NEWER;UNITY_2019_2_OR_NEWER;UNITY_2019_3_OR_NEWER;UNITY_2019_4_OR_NEWER;PLATFORM_ARCH_64;UNITY_64;UNITY_INCLUDE_TESTS;UNITY_ANALYTICS;ENABLE_AR;ENABLE_AUDIO;ENABLE_CACHING;ENABLE_CLOTH;ENABLE_MICROPHONE;ENABLE_MULTIPLE_DISPLAYS;ENABLE_PHYSICS;ENABLE_TEXTURE_STREAMING;ENABLE_UNET;ENABLE_LZMA;ENABLE_UNITYEVENTS;ENABLE_VR;ENABLE_WEBCAM;ENABLE_UNITYWEBREQUEST;ENABLE_WWW;ENABLE_CLOUD_SERVICES;ENABLE_CLOUD_SERVICES_COLLAB;ENABLE_CLOUD_SERVICES_COLLAB_SOFTLOCKS;ENABLE_CLOUD_SERVICES_ADS;ENABLE_CLOUD_SERVICES_USE_WEBREQUEST;ENABLE_CLOUD_SERVICES_CRASH_REPORTING;ENABLE_CLOUD_SERVICES_NATIVE_CRASH_REPORTING;ENABLE_CLOUD_SERVICES_PURCHASING;ENABLE_CLOUD_SERVICES_ANALYTICS;ENABLE_CLOUD_SERVICES_UNET;ENABLE_CLOUD_SERVICES_BUILD;ENABLE_CLOUD_LICENSE;ENABLE_EDITOR_HUB_LICENSE;ENABLE_WEBSOCKET_CLIENT;ENABLE_DIRECTOR_AUDIO;ENABLE_DIRECTOR_TEXTURE;ENABLE_MANAGED_JOBS;ENABLE_MANAGED_TRANSFORM_JOBS;ENABLE_MANAGED_ANIMATION_JOBS;ENABLE_MANAGED_AUDIO_JOBS;INCLUDE_DYNAMIC_GI;ENABLE_MONO_BDWGC;ENABLE_SCRIPTING_GC_WBARRIERS;PLATFORM_SUPPORTS_MONO;RENDER_SOFTWARE_CURSOR;ENABLE_VIDEO;PLATFORM_STANDALONE;PLATFORM_STANDALONE_OSX;UNITY_STANDALONE_OSX;UNITY_STANDALONE;ENABLE_GAMECENTER;ENABLE_RUNTIME_GI;ENABLE_MOVIES;ENABLE_NETWORK;ENABLE_CRUNCH_TEXTURE_COMPRESSION;ENABLE_CLUSTER_SYNC;ENABLE_CLUSTERINPUT;ENABLE_SPATIALTRACKING;ENABLE_WEBSOCKET_HOST;ENABLE_MONO;NET_4_6;ENABLE_PROFILER;UNITY_ASSERTIONS;UNITY_EDITOR;UNITY_EDITOR_64;UNITY_EDITOR_OSX;ENABLE_UNITY_COLLECTIONS_CHECKS;ENABLE_BURST_AOT;UNITY_TEAM_LICENSE;ENABLE_CUSTOM_RENDER_TEXTURE;ENABLE_DIRECTOR;ENABLE_LOCALIZATION;ENABLE_SPRITES;ENABLE_TERRAIN;ENABLE_TILEMAP;ENABLE_TIMELINE;ENABLE_LEGACY_INPUT_MANAGER;CSHARP_7_OR_LATER;CSHARP_7_3_OR_NEWER</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <NoWarn>0169</NoWarn>
    <AllowUnsafeBlocks>False</AllowUnsafeBlocks>
    <TreatWarningsAsErrors>False</TreatWarningsAsErrors>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>Temp\bin\Release\</OutputPath>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <NoWarn>0169</NoWarn>
    <AllowUnsafeBlocks>False</AllowUnsafeBlocks>
    <TreatWarningsAsErrors>False</TreatWarningsAsErrors>
  </PropertyGroup>
  <PropertyGroup>
    <NoConfig>true</NoConfig>
    <NoStdLib>true</NoStdLib>
    <AddAdditionalExplicitAssemblyReferences>false</AddAdditionalExplicitAssemblyReferences>
    <ImplicitlyExpandNETStandardFacades>false</ImplicitlyExpandNETStandardFacades>
    <ImplicitlyExpandDesignTimeFacades>false</ImplicitlyExpandDesignTimeFacades>
  </PropertyGroup>
  <ItemGroup>
     <Compile Include="Assets\Scenes\Demo\AHEManager.cs" />
     <Compile Include="Assets\Scenes\Demo\OneUnityAlertView.cs" />
     <Compile Include="Assets\Scenes\Demo\OneUnityConfig.cs" />
     <Compile Include="Assets\Scenes\Demo\OneUnityGamePage.cs" />
     <Compile Include="Assets\Scenes\Demo\OneUnityHomePage.cs" />
     <Compile Include="Assets\Scenes\Demo\OneUnityManager.cs" />
     <Compile Include="Assets\Scenes\Demo\OneUnityPayItemButton.cs" />
     <Compile Include="Assets\Scenes\Demo\OneUnityResource.cs" />
     <Compile Include="Assets\Scenes\Demo\OneUnityRole.cs" />
     <Compile Include="Assets\Scenes\Demo\OneUnityUtils.cs" />
     <Compile Include="Assets\WMPluginDLL\OneSDK\Android\UnityMainThreadDispatcher.cs" />
     <Compile Include="Assets\WMPluginDLL\OneSDK\Android\function\WMFuncActCode.cs" />
     <Compile Include="Assets\WMPluginDLL\OneSDK\Android\function\WMFuncActCodeCallback.cs" />
     <Compile Include="Assets\WMPluginDLL\OneSDK\Android\function\WMFuncAntiAddiction.cs" />
     <Compile Include="Assets\WMPluginDLL\OneSDK\Android\function\WMFuncAntiAddictionCallback.cs" />
     <Compile Include="Assets\WMPluginDLL\OneSDK\Android\function\WMFuncRedeemCode.cs" />
     <Compile Include="Assets\WMPluginDLL\OneSDK\Android\function\WMFuncRedeemCodeCallback.cs" />
     <Compile Include="Assets\WMPluginDLL\OneSDK\Android\function\WMOneAndroidPermission.cs" />
     <Compile Include="Assets\WMPluginDLL\OneSDK\Android\onecore\WMAndroidOnKeyDownAction.cs" />
     <Compile Include="Assets\WMPluginDLL\OneSDK\Android\onecore\WMOneAndroidCore.cs" />
     <Compile Include="Assets\WMPluginDLL\OneSDK\Android\onecore\WMOneCoreCallback.cs" />
     <Compile Include="Assets\WMPluginDLL\OneSDK\Android\utils\WMAndroidAlertDialogUtils.cs" />
     <Compile Include="Assets\WMPluginDLL\OneSDK\Android\utils\WMAndroidLog.cs" />
     <Compile Include="Assets\WMPluginDLL\OneSDK\Cocoa\WMOneCocoaActCode.cs" />
     <Compile Include="Assets\WMPluginDLL\OneSDK\Cocoa\WMOneCocoaAntiAddiction.cs" />
     <Compile Include="Assets\WMPluginDLL\OneSDK\Cocoa\WMOneCocoaCore.cs" />
     <Compile Include="Assets\WMPluginDLL\OneSDK\Cocoa\WMOneCocoaCoreBridgeAPI.cs" />
     <Compile Include="Assets\WMPluginDLL\OneSDK\Cocoa\WMOneCocoaDelegate.cs" />
     <Compile Include="Assets\WMPluginDLL\OneSDK\Cocoa\WMOneCocoaExtensionBridgeAPI.cs" />
     <Compile Include="Assets\WMPluginDLL\OneSDK\Cocoa\WMOneCocoaPermission.cs" />
     <Compile Include="Assets\WMPluginDLL\OneSDK\Cocoa\WMOneCocoaRedeemCode.cs" />
     <Compile Include="Assets\WMPluginDLL\OneSDK\OpenHarmony\WMOneHarmonyActCode.cs" />
     <Compile Include="Assets\WMPluginDLL\OneSDK\OpenHarmony\WMOneHarmonyAntiAddiction.cs" />
     <Compile Include="Assets\WMPluginDLL\OneSDK\OpenHarmony\WMOneHarmonyCore.cs" />
     <Compile Include="Assets\WMPluginDLL\OneSDK\OpenHarmony\WMOneHarmonyHelper.cs" />
     <Compile Include="Assets\WMPluginDLL\OneSDK\OpenHarmony\WMOneHarmonyModel.cs" />
     <Compile Include="Assets\WMPluginDLL\OneSDK\OpenHarmony\WMOneHarmonyPermission.cs" />
     <Compile Include="Assets\WMPluginDLL\OneSDK\OpenHarmony\WMOneHarmonyRedeemCode.cs" />
     <Compile Include="Assets\WMPluginDLL\OneSDK\Public\WMOneCallback.cs" />
     <Compile Include="Assets\WMPluginDLL\OneSDK\Public\WMOneCore.cs" />
     <Compile Include="Assets\WMPluginDLL\OneSDK\Public\WMOneEnum.cs" />
     <Compile Include="Assets\WMPluginDLL\OneSDK\Public\WMOneInterface.cs" />
     <Compile Include="Assets\WMPluginDLL\OneSDK\Public\WMOneModel.cs" />
     <Compile Include="Assets\WMPluginDLL\OneSDK\Public\WMOneWrapper.cs" />
     <Compile Include="Assets\WMPluginDLL\OneSDK\Utils\WMOneTools.cs" />
     <Compile Include="Assets\WMPluginDLL\OneSDK\Windows\WMOneSdkWrapper.cs" />
     <Compile Include="Assets\WMPluginDLL\OneSDK\Windows\WMOneWindows.cs" />
     <Compile Include="Assets\WMPluginDLL\OneSDK\Windows\WMOneWindowsActAndRedeemCode.cs" />
     <Compile Include="Assets\WMPluginDLL\OneSDK\Windows\WMOneWindowsAntiAddiction.cs" />
     <Compile Include="Assets\WMPluginDLL\OneSDK\Windows\WMOneWindowsWmgpMoblieGameSdk.cs" />
     <None Include="Assets\TextMesh Pro\Shaders\TMPro.cginc" />
     <None Include="Assets\Packages\Newtonsoft.Json\netstandard2.0\Newtonsoft.Json.xml" />
     <None Include="Assets\TextMesh Pro\Shaders\TMP_SDF-Mobile Overlay.shader" />
     <None Include="Assets\WMPluginDLL\OneSDK\Version.txt" />
     <None Include="Assets\TextMesh Pro\Shaders\TMP_Bitmap.shader" />
     <None Include="Assets\TextMesh Pro\Shaders\TMPro_Mobile.cginc" />
     <None Include="Assets\TextMesh Pro\Shaders\TMP_SDF SSD.shader" />
     <None Include="Assets\TextMesh Pro\Shaders\TMP_SDF-Surface-Mobile.shader" />
     <None Include="Assets\TextMesh Pro\Shaders\TMP_SDF-Surface.shader" />
     <None Include="Assets\TextMesh Pro\Sprites\EmojiOne Attribution.txt" />
     <None Include="Assets\TextMesh Pro\Shaders\TMP_Bitmap-Custom-Atlas.shader" />
     <None Include="Assets\TextMesh Pro\Shaders\TMP_SDF.shader" />
     <None Include="Assets\TextMesh Pro\Shaders\TMP_SDF-Mobile SSD.shader" />
     <None Include="Assets\TextMesh Pro\Resources\LineBreaking Leading Characters.txt" />
     <None Include="Assets\TextMesh Pro\Shaders\TMPro_Properties.cginc" />
     <None Include="Assets\Packages\Newtonsoft.Json\netstandard2.0\link.xml" />
     <None Include="Assets\TextMesh Pro\Shaders\TMPro_Surface.cginc" />
     <None Include="Assets\TextMesh Pro\Resources\LineBreaking Following Characters.txt" />
     <None Include="Assets\TextMesh Pro\Shaders\TMP_SDF-Mobile Masking.shader" />
     <None Include="Assets\TextMesh Pro\Shaders\TMP_SDF Overlay.shader" />
     <None Include="Assets\TextMesh Pro\Shaders\TMP_Bitmap-Mobile.shader" />
     <None Include="Assets\TextMesh Pro\Fonts\LiberationSans - OFL.txt" />
     <None Include="Assets\TextMesh Pro\Shaders\TMP_SDF-Mobile.shader" />
     <None Include="Assets\TextMesh Pro\Shaders\TMP_Sprite.shader" />
 <Reference Include="Unity.Timeline.Editor">
 <HintPath>/Users/<USER>/Desktop/pwrdwork/OneSDK/OneSDKUnity/OneSDKUnityDemo/Library/ScriptAssemblies/Unity.Timeline.Editor.dll</HintPath>
 </Reference>
 <Reference Include="com.unity.multiplayer-hlapi.Editor">
 <HintPath>/Users/<USER>/Desktop/pwrdwork/OneSDK/OneSDKUnity/OneSDKUnityDemo/Library/ScriptAssemblies/com.unity.multiplayer-hlapi.Editor.dll</HintPath>
 </Reference>
 <Reference Include="Unity.VSCode.Editor">
 <HintPath>/Users/<USER>/Desktop/pwrdwork/OneSDK/OneSDKUnity/OneSDKUnityDemo/Library/ScriptAssemblies/Unity.VSCode.Editor.dll</HintPath>
 </Reference>
 <Reference Include="Unity.TextMeshPro.Editor">
 <HintPath>/Users/<USER>/Desktop/pwrdwork/OneSDK/OneSDKUnity/OneSDKUnityDemo/Library/ScriptAssemblies/Unity.TextMeshPro.Editor.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.UI">
 <HintPath>/Users/<USER>/Desktop/pwrdwork/OneSDK/OneSDKUnity/OneSDKUnityDemo/Library/ScriptAssemblies/UnityEngine.UI.dll</HintPath>
 </Reference>
 <Reference Include="Unity.Timeline">
 <HintPath>/Users/<USER>/Desktop/pwrdwork/OneSDK/OneSDKUnity/OneSDKUnityDemo/Library/ScriptAssemblies/Unity.Timeline.dll</HintPath>
 </Reference>
 <Reference Include="com.unity.multiplayer-weaver.Editor">
 <HintPath>/Users/<USER>/Desktop/pwrdwork/OneSDK/OneSDKUnity/OneSDKUnityDemo/Library/ScriptAssemblies/com.unity.multiplayer-weaver.Editor.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.XR.LegacyInputHelpers">
 <HintPath>/Users/<USER>/Desktop/pwrdwork/OneSDK/OneSDKUnity/OneSDKUnityDemo/Library/ScriptAssemblies/UnityEngine.XR.LegacyInputHelpers.dll</HintPath>
 </Reference>
 <Reference Include="Unity.Rider.Editor">
 <HintPath>/Users/<USER>/Desktop/pwrdwork/OneSDK/OneSDKUnity/OneSDKUnityDemo/Library/ScriptAssemblies/Unity.Rider.Editor.dll</HintPath>
 </Reference>
 <Reference Include="Analytics">
 <HintPath>/Users/<USER>/Desktop/pwrdwork/OneSDK/OneSDKUnity/OneSDKUnityDemo/Library/ScriptAssemblies/Analytics.dll</HintPath>
 </Reference>
 <Reference Include="Unity.2D.Sprite.Editor">
 <HintPath>/Users/<USER>/Desktop/pwrdwork/OneSDK/OneSDKUnity/OneSDKUnityDemo/Library/ScriptAssemblies/Unity.2D.Sprite.Editor.dll</HintPath>
 </Reference>
 <Reference Include="Unity.2D.Tilemap.Editor">
 <HintPath>/Users/<USER>/Desktop/pwrdwork/OneSDK/OneSDKUnity/OneSDKUnityDemo/Library/ScriptAssemblies/Unity.2D.Tilemap.Editor.dll</HintPath>
 </Reference>
 <Reference Include="UnityEditor.SpatialTracking">
 <HintPath>/Users/<USER>/Desktop/pwrdwork/OneSDK/OneSDKUnity/OneSDKUnityDemo/Library/ScriptAssemblies/UnityEditor.SpatialTracking.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.SpatialTracking">
 <HintPath>/Users/<USER>/Desktop/pwrdwork/OneSDK/OneSDKUnity/OneSDKUnityDemo/Library/ScriptAssemblies/UnityEngine.SpatialTracking.dll</HintPath>
 </Reference>
 <Reference Include="Unity.VisualStudio.Editor">
 <HintPath>/Users/<USER>/Desktop/pwrdwork/OneSDK/OneSDKUnity/OneSDKUnityDemo/Library/ScriptAssemblies/Unity.VisualStudio.Editor.dll</HintPath>
 </Reference>
 <Reference Include="Unity.TextMeshPro">
 <HintPath>/Users/<USER>/Desktop/pwrdwork/OneSDK/OneSDKUnity/OneSDKUnityDemo/Library/ScriptAssemblies/Unity.TextMeshPro.dll</HintPath>
 </Reference>
 <Reference Include="Unity.Analytics.DataPrivacy">
 <HintPath>/Users/<USER>/Desktop/pwrdwork/OneSDK/OneSDKUnity/OneSDKUnityDemo/Library/ScriptAssemblies/Unity.Analytics.DataPrivacy.dll</HintPath>
 </Reference>
 <Reference Include="UnityEditor.XR.LegacyInputHelpers">
 <HintPath>/Users/<USER>/Desktop/pwrdwork/OneSDK/OneSDKUnity/OneSDKUnityDemo/Library/ScriptAssemblies/UnityEditor.XR.LegacyInputHelpers.dll</HintPath>
 </Reference>
 <Reference Include="UnityEditor.UI">
 <HintPath>/Users/<USER>/Desktop/pwrdwork/OneSDK/OneSDKUnity/OneSDKUnityDemo/Library/ScriptAssemblies/UnityEditor.UI.dll</HintPath>
 </Reference>
 <Reference Include="com.unity.multiplayer-hlapi.Runtime">
 <HintPath>/Users/<USER>/Desktop/pwrdwork/OneSDK/OneSDKUnity/OneSDKUnityDemo/Library/ScriptAssemblies/com.unity.multiplayer-hlapi.Runtime.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.AIModule">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AIModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.ARModule">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ARModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.AccessibilityModule">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AccessibilityModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.AndroidJNIModule">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AndroidJNIModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.AnimationModule">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AnimationModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.AssetBundleModule">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AssetBundleModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.AudioModule">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AudioModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.ClothModule">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ClothModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.ClusterInputModule">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ClusterInputModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.ClusterRendererModule">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ClusterRendererModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.CoreModule">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.CoreModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.CrashReportingModule">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.CrashReportingModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.DSPGraphModule">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.DSPGraphModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.DirectorModule">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.DirectorModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.GameCenterModule">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.GameCenterModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.GridModule">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.GridModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.HotReloadModule">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.HotReloadModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.IMGUIModule">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.IMGUIModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.ImageConversionModule">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ImageConversionModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.InputModule">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.InputModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.InputLegacyModule">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.InputLegacyModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.JSONSerializeModule">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.JSONSerializeModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.LocalizationModule">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.LocalizationModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.ParticleSystemModule">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ParticleSystemModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.PerformanceReportingModule">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.PerformanceReportingModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.PhysicsModule">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.PhysicsModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.Physics2DModule">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.Physics2DModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.ProfilerModule">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ProfilerModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.ScreenCaptureModule">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ScreenCaptureModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.SharedInternalsModule">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SharedInternalsModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.SpriteMaskModule">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SpriteMaskModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.SpriteShapeModule">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SpriteShapeModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.StreamingModule">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.StreamingModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.SubstanceModule">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SubstanceModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.SubsystemsModule">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SubsystemsModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.TLSModule">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TLSModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.TerrainModule">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TerrainModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.TerrainPhysicsModule">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TerrainPhysicsModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.TextCoreModule">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TextCoreModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.TextRenderingModule">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TextRenderingModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.TilemapModule">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TilemapModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.UIModule">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UIModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.UIElementsModule">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UIElementsModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.UNETModule">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UNETModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.UmbraModule">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UmbraModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.UnityAnalyticsModule">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityAnalyticsModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.UnityConnectModule">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityConnectModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.UnityTestProtocolModule">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityTestProtocolModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.UnityWebRequestModule">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.UnityWebRequestAssetBundleModule">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestAssetBundleModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.UnityWebRequestAudioModule">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestAudioModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.UnityWebRequestTextureModule">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestTextureModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.UnityWebRequestWWWModule">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestWWWModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.VFXModule">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VFXModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.VRModule">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VRModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.VehiclesModule">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VehiclesModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.VideoModule">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VideoModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.WindModule">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.WindModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.XRModule">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.XRModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEditor">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/Managed/UnityEditor.dll</HintPath>
 </Reference>
 <Reference Include="Newtonsoft.Json">
 <HintPath>/Users/<USER>/Desktop/pwrdwork/OneSDK/OneSDKUnity/OneSDKUnityDemo/Assets/Packages/Newtonsoft.Json/netstandard2.0/Newtonsoft.Json.dll</HintPath>
 </Reference>
 <Reference Include="Unity.Analytics.Tracker">
 <HintPath>/Users/<USER>/Desktop/pwrdwork/OneSDK/OneSDKUnity/OneSDKUnityDemo/Library/PackageCache/com.unity.analytics@3.6.12/Unity.Analytics.Tracker.dll</HintPath>
 </Reference>
 <Reference Include="Unity.Analytics.Editor">
 <HintPath>/Users/<USER>/Desktop/pwrdwork/OneSDK/OneSDKUnity/OneSDKUnityDemo/Library/PackageCache/com.unity.analytics@3.6.12/Unity.Analytics.Editor.dll</HintPath>
 </Reference>
 <Reference Include="Unity.Analytics.StandardEvents">
 <HintPath>/Users/<USER>/Desktop/pwrdwork/OneSDK/OneSDKUnity/OneSDKUnityDemo/Library/PackageCache/com.unity.analytics@3.6.12/AnalyticsStandardEvents/Unity.Analytics.StandardEvents.dll</HintPath>
 </Reference>
 <Reference Include="UnityEditor.iOS.Extensions.Xcode">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/PlaybackEngines/iOSSupport/UnityEditor.iOS.Extensions.Xcode.dll</HintPath>
 </Reference>
 <Reference Include="UnityEditor.iOS.Extensions.Common">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/PlaybackEngines/iOSSupport/UnityEditor.iOS.Extensions.Common.dll</HintPath>
 </Reference>
 <Reference Include="mscorlib">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/mscorlib.dll</HintPath>
 </Reference>
 <Reference Include="System">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/System.dll</HintPath>
 </Reference>
 <Reference Include="System.Core">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/System.Core.dll</HintPath>
 </Reference>
 <Reference Include="System.Runtime.Serialization">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/System.Runtime.Serialization.dll</HintPath>
 </Reference>
 <Reference Include="System.Xml">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/System.Xml.dll</HintPath>
 </Reference>
 <Reference Include="System.Xml.Linq">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/System.Xml.Linq.dll</HintPath>
 </Reference>
 <Reference Include="System.Numerics">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/System.Numerics.dll</HintPath>
 </Reference>
 <Reference Include="System.Numerics.Vectors">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/System.Numerics.Vectors.dll</HintPath>
 </Reference>
 <Reference Include="System.Net.Http">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/System.Net.Http.dll</HintPath>
 </Reference>
 <Reference Include="Microsoft.CSharp">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/Microsoft.CSharp.dll</HintPath>
 </Reference>
 <Reference Include="System.Data">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/System.Data.dll</HintPath>
 </Reference>
 <Reference Include="Microsoft.Win32.Primitives">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/Microsoft.Win32.Primitives.dll</HintPath>
 </Reference>
 <Reference Include="System.AppContext">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.AppContext.dll</HintPath>
 </Reference>
 <Reference Include="System.Collections.Concurrent">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Collections.Concurrent.dll</HintPath>
 </Reference>
 <Reference Include="System.Collections.NonGeneric">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Collections.NonGeneric.dll</HintPath>
 </Reference>
 <Reference Include="System.Collections.Specialized">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Collections.Specialized.dll</HintPath>
 </Reference>
 <Reference Include="System.Collections">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Collections.dll</HintPath>
 </Reference>
 <Reference Include="System.ComponentModel.Annotations">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ComponentModel.Annotations.dll</HintPath>
 </Reference>
 <Reference Include="System.ComponentModel.EventBasedAsync">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ComponentModel.EventBasedAsync.dll</HintPath>
 </Reference>
 <Reference Include="System.ComponentModel.Primitives">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ComponentModel.Primitives.dll</HintPath>
 </Reference>
 <Reference Include="System.ComponentModel.TypeConverter">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ComponentModel.TypeConverter.dll</HintPath>
 </Reference>
 <Reference Include="System.ComponentModel">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ComponentModel.dll</HintPath>
 </Reference>
 <Reference Include="System.Console">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Console.dll</HintPath>
 </Reference>
 <Reference Include="System.Data.Common">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Data.Common.dll</HintPath>
 </Reference>
 <Reference Include="System.Diagnostics.Contracts">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.Contracts.dll</HintPath>
 </Reference>
 <Reference Include="System.Diagnostics.Debug">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.Debug.dll</HintPath>
 </Reference>
 <Reference Include="System.Diagnostics.FileVersionInfo">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.FileVersionInfo.dll</HintPath>
 </Reference>
 <Reference Include="System.Diagnostics.Process">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.Process.dll</HintPath>
 </Reference>
 <Reference Include="System.Diagnostics.StackTrace">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.StackTrace.dll</HintPath>
 </Reference>
 <Reference Include="System.Diagnostics.TextWriterTraceListener">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.TextWriterTraceListener.dll</HintPath>
 </Reference>
 <Reference Include="System.Diagnostics.Tools">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.Tools.dll</HintPath>
 </Reference>
 <Reference Include="System.Diagnostics.TraceSource">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.TraceSource.dll</HintPath>
 </Reference>
 <Reference Include="System.Drawing.Primitives">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Drawing.Primitives.dll</HintPath>
 </Reference>
 <Reference Include="System.Dynamic.Runtime">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Dynamic.Runtime.dll</HintPath>
 </Reference>
 <Reference Include="System.Globalization.Calendars">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Globalization.Calendars.dll</HintPath>
 </Reference>
 <Reference Include="System.Globalization.Extensions">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Globalization.Extensions.dll</HintPath>
 </Reference>
 <Reference Include="System.Globalization">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Globalization.dll</HintPath>
 </Reference>
 <Reference Include="System.IO.Compression.ZipFile">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.Compression.ZipFile.dll</HintPath>
 </Reference>
 <Reference Include="System.IO.FileSystem.DriveInfo">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.FileSystem.DriveInfo.dll</HintPath>
 </Reference>
 <Reference Include="System.IO.FileSystem.Primitives">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.FileSystem.Primitives.dll</HintPath>
 </Reference>
 <Reference Include="System.IO.FileSystem.Watcher">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.FileSystem.Watcher.dll</HintPath>
 </Reference>
 <Reference Include="System.IO.FileSystem">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.FileSystem.dll</HintPath>
 </Reference>
 <Reference Include="System.IO.IsolatedStorage">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.IsolatedStorage.dll</HintPath>
 </Reference>
 <Reference Include="System.IO.MemoryMappedFiles">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.MemoryMappedFiles.dll</HintPath>
 </Reference>
 <Reference Include="System.IO.Pipes">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.Pipes.dll</HintPath>
 </Reference>
 <Reference Include="System.IO.UnmanagedMemoryStream">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.UnmanagedMemoryStream.dll</HintPath>
 </Reference>
 <Reference Include="System.IO">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.dll</HintPath>
 </Reference>
 <Reference Include="System.Linq.Expressions">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Linq.Expressions.dll</HintPath>
 </Reference>
 <Reference Include="System.Linq.Parallel">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Linq.Parallel.dll</HintPath>
 </Reference>
 <Reference Include="System.Linq.Queryable">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Linq.Queryable.dll</HintPath>
 </Reference>
 <Reference Include="System.Linq">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Linq.dll</HintPath>
 </Reference>
 <Reference Include="System.Net.Http.Rtc">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Http.Rtc.dll</HintPath>
 </Reference>
 <Reference Include="System.Net.NameResolution">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.NameResolution.dll</HintPath>
 </Reference>
 <Reference Include="System.Net.NetworkInformation">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.NetworkInformation.dll</HintPath>
 </Reference>
 <Reference Include="System.Net.Ping">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Ping.dll</HintPath>
 </Reference>
 <Reference Include="System.Net.Primitives">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Primitives.dll</HintPath>
 </Reference>
 <Reference Include="System.Net.Requests">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Requests.dll</HintPath>
 </Reference>
 <Reference Include="System.Net.Security">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Security.dll</HintPath>
 </Reference>
 <Reference Include="System.Net.Sockets">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Sockets.dll</HintPath>
 </Reference>
 <Reference Include="System.Net.WebHeaderCollection">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.WebHeaderCollection.dll</HintPath>
 </Reference>
 <Reference Include="System.Net.WebSockets.Client">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.WebSockets.Client.dll</HintPath>
 </Reference>
 <Reference Include="System.Net.WebSockets">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.WebSockets.dll</HintPath>
 </Reference>
 <Reference Include="System.ObjectModel">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ObjectModel.dll</HintPath>
 </Reference>
 <Reference Include="System.Reflection.Emit.ILGeneration">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.Emit.ILGeneration.dll</HintPath>
 </Reference>
 <Reference Include="System.Reflection.Emit.Lightweight">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.Emit.Lightweight.dll</HintPath>
 </Reference>
 <Reference Include="System.Reflection.Emit">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.Emit.dll</HintPath>
 </Reference>
 <Reference Include="System.Reflection.Extensions">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.Extensions.dll</HintPath>
 </Reference>
 <Reference Include="System.Reflection.Primitives">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.Primitives.dll</HintPath>
 </Reference>
 <Reference Include="System.Reflection">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.dll</HintPath>
 </Reference>
 <Reference Include="System.Resources.Reader">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Resources.Reader.dll</HintPath>
 </Reference>
 <Reference Include="System.Resources.ResourceManager">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Resources.ResourceManager.dll</HintPath>
 </Reference>
 <Reference Include="System.Resources.Writer">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Resources.Writer.dll</HintPath>
 </Reference>
 <Reference Include="System.Runtime.CompilerServices.VisualC">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.CompilerServices.VisualC.dll</HintPath>
 </Reference>
 <Reference Include="System.Runtime.Extensions">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Extensions.dll</HintPath>
 </Reference>
 <Reference Include="System.Runtime.Handles">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Handles.dll</HintPath>
 </Reference>
 <Reference Include="System.Runtime.InteropServices.RuntimeInformation">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.InteropServices.RuntimeInformation.dll</HintPath>
 </Reference>
 <Reference Include="System.Runtime.InteropServices.WindowsRuntime">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.InteropServices.WindowsRuntime.dll</HintPath>
 </Reference>
 <Reference Include="System.Runtime.InteropServices">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.InteropServices.dll</HintPath>
 </Reference>
 <Reference Include="System.Runtime.Numerics">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Numerics.dll</HintPath>
 </Reference>
 <Reference Include="System.Runtime.Serialization.Formatters">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Serialization.Formatters.dll</HintPath>
 </Reference>
 <Reference Include="System.Runtime.Serialization.Json">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Serialization.Json.dll</HintPath>
 </Reference>
 <Reference Include="System.Runtime.Serialization.Primitives">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Serialization.Primitives.dll</HintPath>
 </Reference>
 <Reference Include="System.Runtime.Serialization.Xml">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Serialization.Xml.dll</HintPath>
 </Reference>
 <Reference Include="System.Runtime">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.dll</HintPath>
 </Reference>
 <Reference Include="System.Security.Claims">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Claims.dll</HintPath>
 </Reference>
 <Reference Include="System.Security.Cryptography.Algorithms">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Cryptography.Algorithms.dll</HintPath>
 </Reference>
 <Reference Include="System.Security.Cryptography.Csp">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Cryptography.Csp.dll</HintPath>
 </Reference>
 <Reference Include="System.Security.Cryptography.Encoding">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Cryptography.Encoding.dll</HintPath>
 </Reference>
 <Reference Include="System.Security.Cryptography.Primitives">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Cryptography.Primitives.dll</HintPath>
 </Reference>
 <Reference Include="System.Security.Cryptography.X509Certificates">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Cryptography.X509Certificates.dll</HintPath>
 </Reference>
 <Reference Include="System.Security.Principal">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Principal.dll</HintPath>
 </Reference>
 <Reference Include="System.Security.SecureString">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.SecureString.dll</HintPath>
 </Reference>
 <Reference Include="System.ServiceModel.Duplex">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ServiceModel.Duplex.dll</HintPath>
 </Reference>
 <Reference Include="System.ServiceModel.Http">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ServiceModel.Http.dll</HintPath>
 </Reference>
 <Reference Include="System.ServiceModel.NetTcp">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ServiceModel.NetTcp.dll</HintPath>
 </Reference>
 <Reference Include="System.ServiceModel.Primitives">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ServiceModel.Primitives.dll</HintPath>
 </Reference>
 <Reference Include="System.ServiceModel.Security">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ServiceModel.Security.dll</HintPath>
 </Reference>
 <Reference Include="System.Text.Encoding.Extensions">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Text.Encoding.Extensions.dll</HintPath>
 </Reference>
 <Reference Include="System.Text.Encoding">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Text.Encoding.dll</HintPath>
 </Reference>
 <Reference Include="System.Text.RegularExpressions">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Text.RegularExpressions.dll</HintPath>
 </Reference>
 <Reference Include="System.Threading.Overlapped">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.Overlapped.dll</HintPath>
 </Reference>
 <Reference Include="System.Threading.Tasks.Parallel">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.Tasks.Parallel.dll</HintPath>
 </Reference>
 <Reference Include="System.Threading.Tasks">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.Tasks.dll</HintPath>
 </Reference>
 <Reference Include="System.Threading.Thread">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.Thread.dll</HintPath>
 </Reference>
 <Reference Include="System.Threading.ThreadPool">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.ThreadPool.dll</HintPath>
 </Reference>
 <Reference Include="System.Threading.Timer">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.Timer.dll</HintPath>
 </Reference>
 <Reference Include="System.Threading">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.dll</HintPath>
 </Reference>
 <Reference Include="System.ValueTuple">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ValueTuple.dll</HintPath>
 </Reference>
 <Reference Include="System.Xml.ReaderWriter">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.ReaderWriter.dll</HintPath>
 </Reference>
 <Reference Include="System.Xml.XDocument">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.XDocument.dll</HintPath>
 </Reference>
 <Reference Include="System.Xml.XPath.XDocument">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.XPath.XDocument.dll</HintPath>
 </Reference>
 <Reference Include="System.Xml.XPath">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.XPath.dll</HintPath>
 </Reference>
 <Reference Include="System.Xml.XmlDocument">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.XmlDocument.dll</HintPath>
 </Reference>
 <Reference Include="System.Xml.XmlSerializer">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.XmlSerializer.dll</HintPath>
 </Reference>
 <Reference Include="netstandard">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/netstandard.dll</HintPath>
 </Reference>
 <Reference Include="UnityScript">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityscript/UnityScript.dll</HintPath>
 </Reference>
 <Reference Include="UnityScript.Lang">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityscript/UnityScript.Lang.dll</HintPath>
 </Reference>
 <Reference Include="Boo.Lang">
 <HintPath>/Applications/Unity/Hub/Editor/2019.4.26f1c1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityscript/Boo.Lang.dll</HintPath>
 </Reference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it.
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>
