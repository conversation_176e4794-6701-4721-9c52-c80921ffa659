
# OneTools辅助工具

## 支持的平台

- Windows
- Mac

## 接入方式

该命令行工具需要在 `dev平台` 下载、解压，并存放到 `Unity/UE` 项目工程目录下，后续对该工具的升级，可直接通过 `upgrade` 命令进行升级即可。

   
## 当前支持的命令

```shell
$ ./onetools help

ios sdk命令行辅助工具，
主要提供以下功能：
  native sdk安装
  依赖sdk版本号显示
  虚幻引擎UBT源码修改
  IPA包信息显示
  符号扫描
  crash日志符号化


Usage:
  onetools [command]

Available Commands:
  completion  Generate the autocompletion script for the specified shell
  crash       针对crashLog进行符号化，也可查看crash相关文件UUID
  help        Help about any command
  install     安装iOS nativeSDK
  plugin      安装U3D(SDK导入/iOS打包)插件
  symbol      针对ipa文件或目录进行目标符号扫描
  ubt         修改UBT源码，以支持各平台部分功能
  upgrade     onetools工具升级命令，将自动升级到最新版本
  version     onetools版本号

Flags:
  -h, --help     help for onetools
  -t, --toggle   Help message for toggle
```

## upgrade

注意：该命令用于升级onetools命令工具

### Mac平台

```shell
engine_project~> onetools/onetools upgrade
```

### Windows平台

```shell
engine_project~> onetools\onetools.exe upgrade
```

## install

### Mac平台

#### 帮助

```shell
engine_project~> onetools/onetools install -h
```

#### 更新gradlle本地缓存

> 注意：<br>
> 1、该操作需要访问远端仓库，因此比较耗时，除非SDK研发人员要求更新，否则尽量少使用该命令。<br>
> 2、对于 `UE` 项目，执行完该命令后，需要研发执行 `安装SDK` 的命令，重新下载SDK。<br>

```shell
engine_project~> onetools/onetools install -U
```

#### 安装SDK

> 注意：由于 `Unity` 项目必须接入 `导入插件` ，因此对于 `Unity` 开发人员而言，该操作无需进行；

```shell
# 安装iOS端的SDK
engine_project~> onetools/onetools install 

# 安装Mac端的SDK
engine_project~> onetools/onetools install --os mac
```

### Windows平台

#### 帮助

```shell
engine_project~> onetools\onetools.exe install -h
```

#### 更新gradlle本地缓存

> 注意：<br>
> 1、该操作需要访问远端仓库，因此比较耗时，除非SDK研发人员要求更新，否则尽量少使用该命令。<br>
> 2、对于 `UE` 项目，执行完该命令后，需要研发执行 `安装SDK` 的命令，重新下载SDK。<br>

```shell
engine_project~> onetools\onetools.exe install -U
```

#### 安装SDK

```shell
# 安装iOS端的SDK
engine_project~> onetools\onetools.exe install 

# 安装Mac端的SDK
engine_project~> onetools\onetools.exe install --os mac
```

## plugin（仅适用 U3D 项目）

该命令用于安装 `U3D` 工具插件，目前支持安装的插件有如下几个：

|插件|功能|是否必须安装|
|---|---|:---:|
|导入插件|该插件用于下载配置文件、SDK等资源，无需游戏手动执行|是|
|工程导出插件|该插件用于游戏打包，需要游戏研发根据需求手动设置相关的工程配置|否|

### Mac平台

#### 帮助

```shell
unity_project~> onetools/onetools plugin -h
```
#### 查看插件版本

```shell
unity_project~> onetools/onetools plugin --list
```

#### 安装（导入插件）

```shell
# 安装最新版本
unity_project~> onetools/onetools plugin -i

# 安装指定版本
unity_project~> onetools/onetools plugin -i -v 版本号
```

#### 安装（工程导出插件）

```shell
# 安装最新版本
unity_project~> onetools/onetools plugin -i -e

# 安装指定版本
unity_project~> onetools/onetools plugin -i -e -v 版本号
```

#### 卸载（导入插件）

```shell
unity_project~> onetools/onetools plugin -u
```

#### 卸载（工程导出插件）

```shell
unity_project~> onetools/onetools plugin -u -e
```

### Windows平台

#### 帮助

```shell
unity_project~> onetools\onetools.exe plugin -h
```

#### 查看插件版本

```shell
unity_project~> onetools\onetools.exe plugin --list
```

#### 安装（导入插件）
 
```shell
# 安装最新版本
unity_project~> onetools\onetools.exe plugin -i

# 安装指定版本
unity_project~> onetools\onetools.exe plugin -i -v 版本号
```

#### 安装（工程导出插件）

```shell
# 安装最新版本
unity_project~> onetools\onetools.exe plugin -i -e

# 安装指定版本
unity_project~> onetools\onetools.exe plugin -i -e -v 版本号
```

#### 卸载（导入插件）

```shell
unity_project~> onetools\onetools.exe plugin -u
```

#### 卸载（工程导出插件）

```shell
unity_project~> onetools\onetools.exe plugin -u -e
```
