
## oneTools辅助工具

项目初衷，将开发中常用的一些命令(定位问题的，减少游戏接入成本的工具脚本等)集合到一起，能提供给游戏开发使用，出现问题时，可先通过工具进行简单排查。

支持windows、macOS平台命令行，针对U3D、Unreal Engine和原生项目，提供以下功能：

- native sdk安装,自动解决相同库引用冲突
- 依赖sdk版本号显示及上报
- 虚幻引擎UBT源码修改，以弥补UE引擎针对iOS支持不足
- ipa包分析

## 项目依赖环境

- go

    > 使用brew或下载pkg安装
    > 下载地址：https://golang.google.cn/dl/

- cobra
 
     > go install github.com/spf13/cobra-cli@latest
     
## 将命令添加到项目

以`install`命令为例:
    
    > ~/go/bin/cobra-cli add install

## 测试
    > go run ./ install -h

## 打包

- macOS
    > GOARCH=amd64 go build
    
- Windos
    > GO_ENABLED=0 GOOS=windows GOARCH=amd64 go build
    
## 当前支持的命令

```shell
$ ./onetools help

ios sdk命令行辅助工具，
主要提供以下功能：
	native sdk安装
	依赖sdk版本号显示
	虚幻引擎UBT源码修改
	IPA包信息显示
	符号扫描
	crash日志符号化

Usage:
  onetools [command]

Available Commands:
  completion  Generate the autocompletion script for the specified shell
  crash       针对crashLog进行符号化，也可查看crash相关文件UUID
  help        Help about any command
  install     安装iOS nativeSDK
  ipa         IPA包扫描，显示Info.plist、Capability等信息
  ipacheck    ipa 分析工具
  symbol      针对ipa文件或目录进行目标符号扫描
  ubt         修改UBT源码，以支持各平台部分功能
  version     onetools版本号

Flags:
  -h, --help     help for onetools
  -t, --toggle   Help message for toggle
```