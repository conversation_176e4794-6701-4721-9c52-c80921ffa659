#!/bin/sh

# 归档目录
archieve_dir="package"
zip_dir="onetools"
archieve_path=$archieve_dir/$zip_dir

rm -rf $archieve_dir
mkdir -p $archieve_path

go mod tidy

# Git版本号
git_version=`git log --abbrev-commit|head -1|cut -d' ' -f 2`

# build macOS 可执行文件
GOOS=darwin GOARCH=amd64 go build -ldflags "-X main._COMMITID_=$git_version" -o $archieve_path/onetools

# build window exe
GO_ENABLED=0 GOOS=windows GOARCH=amd64 go build -ldflags "-X main._COMMITID_=$git_version"  -o $archieve_path/onetools.exe

# 是否需要对onetools进行公证
need_notarization="false"
if [ $# -gt 0 ]; then
  need_notarization="$1"
fi

if [ "$need_notarization" = "true" ]; then
    source $(pwd)/notarization/notarization.sh $archieve_path
fi


# 拷贝Gradle文件
cp -rf Gradle/ $archieve_path/Gradle

sdk_version=`awk -F= '/OneToolsVersion.*=/{print $2}' cmd/version.go | sed 's/[" ]//g'`

# zip压缩
pushd $archieve_dir
archieve_zip_name="onetools_V${sdk_version}_${git_version}.zip"
zip -qry ${archieve_zip_name} $zip_dir
#rm -rf $zip_dir
popd

