#!/bin/bash
# macos.app公证脚本

echo "notarization......"
PRODUCT_NAME="onetools"

PACKAGE_ONETOOLS_PATH=$(pwd)/$1
BUILD_DIR=$(pwd)/notarization/build
rm -rf ${BUILD_DIR}
mkdir -p ${BUILD_DIR}

cp ${PACKAGE_ONETOOLS_PATH}/${PRODUCT_NAME} ${BUILD_DIR}

# 打包工程，需要改成各自项目的
CODE_SIGN_IDENTITY="Developer ID Application: Chongqing MeiQi Interactive Technology Co., Ltd. (65K25Y3ZV4)"
ENTITLEMENTS_FILE="$(pwd)/notarization/onetools.entitlements"

pushd $BUILD_DIR
echo $(pwd)

APP_PATH="$PRODUCT_NAME"
ZIP_PATH="$PRODUCT_NAME.zip"

# 重新签名，添加Hardened Runtime
codesign -s "${CODE_SIGN_IDENTITY}"  --entitlements  "${ENTITLEMENTS_FILE}" -f --options runtime --timestamp  "${APP_PATH}"

# .app打包成.zip
/usr/bin/ditto -c -k "$APP_PATH" "$ZIP_PATH"
echo "----------begin notarytool ---------------"
# Submit the finished deliverables for notarization.
# Wait up to 2 hours for a response.
# Use verbose logging in order to file feedback if an error occurs.
NOTARIZATION_RESPONSE="${BUILD_DIR}/NotarizationResponse.plist"
KEYCHAIN_PROFILE="notarytool-new-password"
xcrun notarytool submit --keychain-profile ${KEYCHAIN_PROFILE}  --verbose "$ZIP_PATH" --wait --timeout 2h --output-format plist > "$NOTARIZATION_RESPONSE"

return_code=$?

if [ $return_code -eq 0 ]; then
	message=`/usr/libexec/PlistBuddy -c "Print :message" "$NOTARIZATION_RESPONSE"`
	status=`/usr/libexec/PlistBuddy -c "Print :status" "$NOTARIZATION_RESPONSE"`

	# 对app进行stapler将票证附加到.app
	xcrun stapler staple "$ZIP_PATH"

	unzip $ZIP_PATH -d Archive
	mv -f Archive/${PRODUCT_NAME} ${PACKAGE_ONETOOLS_PATH}

	# 查看公证状态
	# spctl -vvv --assess --type exec "$APP_PATH"
    echo "----------finish notarytool success---------------"
else
	message="An Error Occurred."
	status="Check Xcode log."

	cat $NOTARIZATION_RESPONSE
 
    echo "----------finish notarytool failure---------------"
    exit 1
fi

popd
