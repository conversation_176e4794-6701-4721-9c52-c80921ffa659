package main

import (
	"fmt"
	"os"
	"path/filepath"
	"onetools/cmd/ipa"
	"onetools/cmd/utility"
)

func main() {
	// 创建测试目录结构
	testDir := "test_unit_add_files"
	sourceDir := filepath.Join(testDir, "source")
	targetDir := filepath.Join(testDir, "target")
	
	// 清理之前的测试目录
	os.RemoveAll(testDir)
	
	// 创建源目录结构
	os.MkdirAll(filepath.Join(sourceDir, "Frameworks"), 0755)
	os.MkdirAll(filepath.Join(sourceDir, "Resources"), 0755)
	os.MkdirAll(filepath.Join(sourceDir, "test.bundle"), 0755)
	os.MkdirAll(filepath.Join(sourceDir, "zh-Hans.lproj"), 0755)
	
	// 创建测试文件
	writeTestFile(filepath.Join(sourceDir, "test_root_file.txt"), "Root file content")
	writeTestFile(filepath.Join(sourceDir, "Frameworks", "TestFramework.framework"), "Framework content")
	writeTestFile(filepath.Join(sourceDir, "Resources", "test_resource.txt"), "Resource content")
	writeTestFile(filepath.Join(sourceDir, "test.bundle", "bundle_file.txt"), "Bundle content")
	writeTestFile(filepath.Join(sourceDir, "zh-Hans.lproj", "Localizable.strings"), "Localization content")
	
	// 创建目标目录（模拟.app目录）
	os.MkdirAll(targetDir, 0755)
	
	// 创建一些现有文件来测试覆盖功能
	writeTestFile(filepath.Join(targetDir, "existing_file.txt"), "Existing content")
	os.MkdirAll(filepath.Join(targetDir, "existing_dir"), 0755)
	writeTestFile(filepath.Join(targetDir, "existing_dir", "existing_sub_file.txt"), "Existing sub content")
	
	fmt.Printf("测试目录结构已创建:\n")
	fmt.Printf("源目录: %s\n", sourceDir)
	fmt.Printf("目标目录: %s\n", targetDir)
	
	// 显示源目录结构
	fmt.Printf("\n源目录文件结构:\n")
	filepath.Walk(sourceDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		if !info.IsDir() {
			relPath, _ := filepath.Rel(sourceDir, path)
			fmt.Printf("  %s\n", relPath)
		}
		return nil
	})
	
	// 显示目标目录初始结构
	fmt.Printf("\n目标目录初始结构:\n")
	filepath.Walk(targetDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		if !info.IsDir() {
			relPath, _ := filepath.Rel(targetDir, path)
			fmt.Printf("  %s\n", relPath)
		}
		return nil
	})
	
	// 测试添加文件功能
	fmt.Printf("\n开始测试添加文件功能...\n")
	
	// 这里我们需要直接调用addFilesToApp函数
	// 但由于它是包私有的，我们需要创建一个公共的测试函数
	// 或者直接在这里复制逻辑进行测试
	
	fmt.Printf("注意: 这是一个单元测试框架，实际的addFilesToApp函数是包私有的\n")
	fmt.Printf("请运行完整的重签名测试来验证功能\n")
	
	// 清理测试目录
	defer os.RemoveAll(testDir)
}

func writeTestFile(path, content string) {
	dir := filepath.Dir(path)
	os.MkdirAll(dir, 0755)
	os.WriteFile(path, []byte(content), 0644)
}
